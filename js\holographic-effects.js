/**
 * Holographic Effects 2025
 * Interactive holographic animations and mouse-following effects
 */

class HolographicEffect {
  constructor(element, options = {}) {
    this.element = element;
    this.options = {
      intensity: options.intensity || 1,
      speed: options.speed || 1,
      mouseFollow: options.mouseFollow || true,
      colorShift: options.colorShift || true,
      ...options
    };
    
    this.mouse = { x: 0, y: 0 };
    this.elementRect = null;
    
    this.init();
  }

  init() {
    this.setupElement();
    this.bindEvents();
    this.startAnimation();
  }

  setupElement() {
    this.element.classList.add('holographic-interactive');
    this.element.style.position = 'relative';
    this.element.style.overflow = 'hidden';
    
    // Create holographic overlay
    this.overlay = document.createElement('div');
    this.overlay.className = 'holo-overlay';
    this.overlay.style.cssText = `
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      pointer-events: none;
      background: linear-gradient(45deg, 
        rgba(255, 0, 110, 0.1),
        rgba(131, 56, 236, 0.1),
        rgba(58, 134, 255, 0.1),
        rgba(6, 255, 165, 0.1),
        rgba(255, 190, 11, 0.1)
      );
      background-size: 200% 200%;
      opacity: 0;
      transition: opacity 0.3s ease;
    `;
    
    this.element.appendChild(this.overlay);
  }

  bindEvents() {
    if (this.options.mouseFollow) {
      this.element.addEventListener('mouseenter', this.onMouseEnter.bind(this));
      this.element.addEventListener('mouseleave', this.onMouseLeave.bind(this));
      this.element.addEventListener('mousemove', this.onMouseMove.bind(this));
    }
  }

  onMouseEnter(e) {
    this.elementRect = this.element.getBoundingClientRect();
    this.overlay.style.opacity = '1';
    this.element.style.transform = 'translateZ(0)';
  }

  onMouseLeave(e) {
    this.overlay.style.opacity = '0';
    this.element.style.transform = 'translateZ(0) rotateX(0deg) rotateY(0deg)';
    this.overlay.style.background = `linear-gradient(45deg, 
      rgba(255, 0, 110, 0.1),
      rgba(131, 56, 236, 0.1),
      rgba(58, 134, 255, 0.1),
      rgba(6, 255, 165, 0.1),
      rgba(255, 190, 11, 0.1)
    )`;
  }

  onMouseMove(e) {
    if (!this.elementRect) return;
    
    const x = e.clientX - this.elementRect.left;
    const y = e.clientY - this.elementRect.top;
    
    const centerX = this.elementRect.width / 2;
    const centerY = this.elementRect.height / 2;
    
    const rotateX = (y - centerY) / centerY * 10 * this.options.intensity;
    const rotateY = (centerX - x) / centerX * 10 * this.options.intensity;
    
    this.element.style.transform = `translateZ(0) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
    
    // Update holographic gradient based on mouse position
    const hue = (x / this.elementRect.width) * 360;
    const saturation = 70 + (y / this.elementRect.height) * 30;
    
    if (this.options.colorShift) {
      this.overlay.style.background = `
        radial-gradient(circle at ${x}px ${y}px, 
          hsla(${hue}, ${saturation}%, 60%, 0.2) 0%,
          hsla(${hue + 60}, ${saturation}%, 60%, 0.1) 50%,
          transparent 100%
        )
      `;
    }
  }

  startAnimation() {
    let startTime = Date.now();
    
    const animate = () => {
      const elapsed = (Date.now() - startTime) * this.options.speed;
      const backgroundPosition = (elapsed * 0.05) % 200;
      
      this.overlay.style.backgroundPosition = `${backgroundPosition}% ${backgroundPosition}%`;
      
      requestAnimationFrame(animate);
    };
    
    animate();
  }
}

// Holographic Text Effect
class HolographicText {
  constructor(element, options = {}) {
    this.element = element;
    this.text = element.textContent;
    this.options = {
      glitchIntensity: options.glitchIntensity || 0.1,
      colorCycle: options.colorCycle || true,
      ...options
    };
    
    this.init();
  }

  init() {
    this.element.setAttribute('data-text', this.text);
    this.element.classList.add('holo-text-interactive');
    
    if (this.options.colorCycle) {
      this.startColorCycle();
    }
    
    this.bindEvents();
  }

  bindEvents() {
    this.element.addEventListener('mouseenter', this.onHover.bind(this));
    this.element.addEventListener('mouseleave', this.onLeave.bind(this));
  }

  onHover() {
    this.element.style.animation = 'holographicGlitch 0.5s ease-in-out';
    
    setTimeout(() => {
      this.element.style.animation = 'holographicTextFlow 3s linear infinite';
    }, 500);
  }

  onLeave() {
    this.element.style.animation = 'holographicTextFlow 3s linear infinite';
  }

  startColorCycle() {
    let hue = 0;
    
    const cycle = () => {
      hue = (hue + 1) % 360;
      
      this.element.style.background = `linear-gradient(90deg,
        hsl(${hue}, 80%, 60%) 0%,
        hsl(${(hue + 60) % 360}, 80%, 60%) 20%,
        hsl(${(hue + 120) % 360}, 80%, 60%) 40%,
        hsl(${(hue + 180) % 360}, 80%, 60%) 60%,
        hsl(${(hue + 240) % 360}, 80%, 60%) 80%,
        hsl(${hue}, 80%, 60%) 100%
      )`;
      
      requestAnimationFrame(cycle);
    };
    
    cycle();
  }
}

// Holographic Particle System
class HolographicParticles {
  constructor(container, options = {}) {
    this.container = container;
    this.options = {
      particleCount: options.particleCount || 20,
      speed: options.speed || 1,
      colors: options.colors || ['#ff006e', '#8338ec', '#3a86ff', '#06ffa5', '#ffbe0b'],
      ...options
    };
    
    this.particles = [];
    this.init();
  }

  init() {
    this.container.classList.add('holo-particles-container');
    this.container.style.position = 'relative';
    this.container.style.overflow = 'hidden';
    
    this.createParticles();
    this.animate();
  }

  createParticles() {
    for (let i = 0; i < this.options.particleCount; i++) {
      const particle = document.createElement('div');
      particle.className = 'holo-particle-dynamic';
      
      const color = this.options.colors[Math.floor(Math.random() * this.options.colors.length)];
      const size = Math.random() * 6 + 2;
      const x = Math.random() * this.container.offsetWidth;
      const y = this.container.offsetHeight + 20;
      
      particle.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        background: ${color};
        border-radius: 50%;
        left: ${x}px;
        top: ${y}px;
        box-shadow: 0 0 ${size * 2}px ${color};
        pointer-events: none;
      `;
      
      this.container.appendChild(particle);
      
      this.particles.push({
        element: particle,
        x: x,
        y: y,
        speed: Math.random() * 2 + 1,
        size: size,
        color: color,
        opacity: Math.random() * 0.8 + 0.2
      });
    }
  }

  animate() {
    this.particles.forEach((particle, index) => {
      particle.y -= particle.speed * this.options.speed;
      particle.opacity = Math.max(0, particle.opacity - 0.005);
      
      particle.element.style.top = `${particle.y}px`;
      particle.element.style.opacity = particle.opacity;
      
      // Reset particle when it goes off screen
      if (particle.y < -20 || particle.opacity <= 0) {
        particle.y = this.container.offsetHeight + 20;
        particle.x = Math.random() * this.container.offsetWidth;
        particle.opacity = Math.random() * 0.8 + 0.2;
        particle.element.style.left = `${particle.x}px`;
      }
    });
    
    requestAnimationFrame(() => this.animate());
  }
}

// Auto-initialize holographic effects
document.addEventListener('DOMContentLoaded', () => {
  // Initialize holographic cards
  const holoCards = document.querySelectorAll('.holo-card, .holographic-card');
  holoCards.forEach(card => {
    new HolographicEffect(card, {
      intensity: 0.8,
      mouseFollow: true,
      colorShift: true
    });
  });

  // Initialize holographic text
  const holoTexts = document.querySelectorAll('.holo-text, .holographic-text');
  holoTexts.forEach(text => {
    new HolographicText(text, {
      glitchIntensity: 0.1,
      colorCycle: true
    });
  });

  // Initialize holographic buttons
  const holoButtons = document.querySelectorAll('.btn-holo, .holographic-button');
  holoButtons.forEach(button => {
    new HolographicEffect(button, {
      intensity: 0.5,
      mouseFollow: true,
      colorShift: false
    });
  });

  // Initialize particle systems
  const particleContainers = document.querySelectorAll('.holo-particles');
  particleContainers.forEach(container => {
    new HolographicParticles(container, {
      particleCount: 15,
      speed: 1.2
    });
  });
});

// Export classes for external use
window.HolographicEffects = {
  HolographicEffect,
  HolographicText,
  HolographicParticles
};
