/*
 * Performance Fixes CSS
 * Lightweight alternatives to heavy JavaScript animations
 */

/* Enhanced static gradient background for low-performance devices */
.static-gradient-bg {
  background:
    radial-gradient(circle at top right, rgba(157, 78, 221, 0.4), transparent 50%),
    radial-gradient(circle at bottom left, rgba(0, 180, 216, 0.4), transparent 50%),
    radial-gradient(circle at center, rgba(157, 78, 221, 0.1), transparent 70%),
    linear-gradient(to bottom right, #0a0a12, #12121e);
  position: relative;
  overflow: hidden;
}

/* Add subtle animation to the static background */
.static-gradient-bg::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  right: -50%;
  bottom: -50%;
  width: 200%;
  height: 200%;
  background:
    radial-gradient(circle at 30% 40%, rgba(157, 78, 221, 0.1), transparent 30%),
    radial-gradient(circle at 70% 60%, rgba(0, 180, 216, 0.1), transparent 30%);
  animation: rotateGradient 30s linear infinite;
  z-index: 0;
}

@keyframes rotateGradient {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Enhanced particles effect (CSS-only alternative to Three.js) */
.light-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.particle {
  position: absolute;
  width: 5px;
  height: 5px;
  background: rgba(157, 78, 221, 0.6);
  border-radius: 50%;
  pointer-events: none;
  opacity: 0.6;
  box-shadow: 0 0 10px rgba(157, 78, 221, 0.4);
  animation: float-particle linear infinite;
}

.particle:nth-child(even) {
  background: rgba(0, 180, 216, 0.6);
  width: 4px;
  height: 4px;
  box-shadow: 0 0 8px rgba(0, 180, 216, 0.4);
}

.particle:nth-child(3n) {
  background: rgba(255, 215, 0, 0.4);
  width: 3px;
  height: 3px;
  box-shadow: 0 0 12px rgba(255, 215, 0, 0.3);
}

.particle:nth-child(5n) {
  width: 2px;
  height: 2px;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
}

@keyframes float-particle {
  0% {
    transform: translateY(0) translateX(0);
    opacity: 0;
  }
  10% {
    opacity: 0.7;
  }
  50% {
    transform: translateY(-50vh) translateX(10px);
  }
  90% {
    opacity: 0.7;
  }
  100% {
    transform: translateY(-100vh) translateX(20px);
    opacity: 0;
  }
}

/* Add more particles for a richer effect */
.hero-section.static-gradient-bg::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 30%, rgba(157, 78, 221, 0.2) 0%, transparent 8px),
    radial-gradient(circle at 75% 20%, rgba(0, 180, 216, 0.2) 0%, transparent 10px),
    radial-gradient(circle at 40% 80%, rgba(255, 215, 0, 0.2) 0%, transparent 6px),
    radial-gradient(circle at 85% 70%, rgba(157, 78, 221, 0.2) 0%, transparent 12px);
  background-size: 180px 180px;
  background-repeat: repeat;
  opacity: 0.5;
  z-index: 0;
  animation: shimmer 60s linear infinite;
}

@keyframes shimmer {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 500px 500px;
  }
}

/* Manual animations for critical sections */
.manual-animate {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.manual-animate.visible {
  opacity: 1;
  transform: translateY(0);
}

.manual-animate.fade-up.visible {
  transform: translateY(0);
}

.manual-animate.fade-left.visible {
  transform: translateX(0);
}

.manual-animate.fade-right.visible {
  transform: translateX(0);
}

.manual-animate.fade-left {
  transform: translateX(-30px);
}

.manual-animate.fade-right {
  transform: translateX(30px);
}

/* Section reveal animations */
section {
  opacity: 0.6;
  transform: translateY(20px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

section.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* Ensure all sections are visible */
section {
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
  transform: none !important;
}

/* Force AOS animations to be visible */
[data-aos] {
  opacity: 1 !important;
  transform: none !important;
  transition: none !important;
}

/* Ensure hero section is always visible */
.hero-section {
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
}

/* Ensure hero content is visible */
.hero-content, .hero-text, .hero-image {
  opacity: 1 !important;
  visibility: visible !important;
  transform: none !important;
}

/* Ensure about section is visible */
#aboutMe {
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
}

/* Ensure skills section is visible */
#skills {
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
}

/* Add subtle highlight to sections when in viewport */
section:not(.hero-section)::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(157, 78, 221, 0.05), transparent 70%);
  pointer-events: none;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.5s ease;
}

section.in-viewport::before {
  opacity: 1;
  animation: pulse-highlight 3s ease-in-out infinite;
}

@keyframes pulse-highlight {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.7;
  }
}

/* Optimize testimonials section */
.testimonial {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.testimonial:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.testimonial-author {
  display: flex;
  align-items: center;
  margin-top: 20px;
}

.author-info {
  display: flex;
  flex-direction: column;
  margin-left: 0;
}

/* Optimize project cards */
.project-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* Optimize buttons */
.btn {
  transition: transform 0.2s ease, box-shadow 0.2s ease, background-color 0.2s ease;
}

.btn:hover {
  transform: translateY(-3px);
}

.btn:active {
  transform: translateY(0);
}

/* Optimize header */
.main-header {
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.main-header.sticky {
  background-color: rgba(10, 10, 18, 0.9);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

/* Optimize back to top button */
.back-to-top {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Media queries for better performance on smaller screens */
@media (max-width: 768px) {
  .particle {
    display: none;
  }

  .manual-animate {
    transition: opacity 0.4s ease, transform 0.4s ease;
  }

  section {
    transition: opacity 0.5s ease, transform 0.5s ease;
  }
}
