/* 
 * Tabs and Timeline Styles for 2025 Portfolio
 * Advanced CSS for tabs and timeline components
 */

/* ===== Tabs Component ===== */
.skills-tabs {
  margin-bottom: var(--spacing-2xl);
}

.tabs-nav {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
  flex-wrap: wrap;
}

.tab-btn {
  background: rgba(31, 31, 31, 0.5);
  border: 1px solid rgba(138, 43, 226, 0.1);
  color: var(--text-secondary);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--border-radius-full);
  cursor: pointer;
  transition: all var(--transition-normal);
  font-weight: var(--font-medium);
  backdrop-filter: blur(10px);
}

.tab-btn:hover {
  border-color: rgba(138, 43, 226, 0.3);
  color: var(--text-primary);
  transform: translateY(-2px);
}

.tab-btn.active {
  background: linear-gradient(45deg, rgba(138, 43, 226, 0.2), rgba(0, 207, 255, 0.2));
  border-color: rgba(138, 43, 226, 0.5);
  color: var(--text-primary);
  box-shadow: 0 0 15px rgba(138, 43, 226, 0.3);
}

.tabs-content {
  position: relative;
  min-height: 300px;
}

.tab-pane {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.5s ease, visibility 0.5s ease;
  transform: translateY(20px);
}

.tab-pane.active {
  opacity: 1;
  visibility: visible;
  position: relative;
  transform: translateY(0);
  transition: opacity 0.5s ease, transform 0.5s ease, visibility 0.5s ease;
}

/* ===== Timeline Component ===== */
.timeline {
  position: relative;
  margin: var(--spacing-xl) 0;
  padding-left: var(--spacing-lg);
}

.timeline::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 2px;
  height: 100%;
  background: linear-gradient(to bottom, var(--accent-primary), var(--accent-secondary));
  border-radius: var(--border-radius-full);
}

.timeline-item {
  position: relative;
  padding-bottom: var(--spacing-xl);
}

.timeline-item:last-child {
  padding-bottom: 0;
}

.timeline-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -8px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--accent-primary), var(--accent-secondary));
  box-shadow: 0 0 10px rgba(138, 43, 226, 0.5);
  z-index: 1;
}

.timeline-content {
  position: relative;
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-md);
  margin-left: var(--spacing-lg);
}

.timeline-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: -10px;
  width: 20px;
  height: 20px;
  background: inherit;
  transform: rotate(45deg);
  border-radius: 2px;
  z-index: -1;
}

/* ===== Timeline Animations ===== */
.timeline-item {
  opacity: 0;
  transform: translateX(30px);
  transition: all 0.5s ease;
}

.timeline-item.aos-animate {
  opacity: 1;
  transform: translateX(0);
}

/* ===== Media Queries ===== */
@media (max-width: 768px) {
  .tabs-nav {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .tab-btn {
    margin-bottom: var(--spacing-sm);
  }
  
  .timeline {
    padding-left: var(--spacing-md);
  }
  
  .timeline-content {
    margin-left: var(--spacing-md);
  }
}

/* ===== Light Theme Overrides ===== */
[data-theme="light"] {
  .tab-btn {
    background: rgba(255, 255, 255, 0.7);
    color: var(--text-tertiary);
  }
  
  .tab-btn.active {
    background: linear-gradient(45deg, rgba(138, 43, 226, 0.1), rgba(0, 207, 255, 0.1));
    color: var(--accent-primary);
  }
  
  .timeline::before {
    background: linear-gradient(to bottom, var(--accent-primary), var(--accent-secondary));
  }
}
