/* مكونات واجهة المستخدم المتقدمة */

/* القائمة الرئيسية */
.main-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  transition: all var(--transition-normal);
  padding: var(--spacing-md) 0;
}

.main-header.sticky {
  background-color: rgba(15, 23, 42, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-sm) 0;
}

.main-header.hide {
  transform: translateY(-100%);
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.logo img {
  height: 40px;
  transition: all var(--transition-normal);
}

.main-header.sticky .logo img {
  height: 32px;
}

.nav-list {
  display: flex;
  gap: var(--spacing-lg);
  list-style: none;
}

.nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: var(--font-medium);
  position: relative;
  padding: var(--spacing-xs) 0;
  transition: all var(--transition-normal);
}

.nav-link:hover, .nav-link.active {
  color: var(--accent-primary);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--accent-primary);
  transition: width var(--transition-normal);
}

.nav-link:hover::after, .nav-link.active::after {
  width: 100%;
}

/* زر تبديل القائمة المتنقلة */
.menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: space-between;
  width: 30px;
  height: 21px;
  cursor: pointer;
  z-index: 110;
}

.menu-toggle span {
  display: block;
  width: 100%;
  height: 3px;
  background-color: var(--text-primary);
  border-radius: 3px;
  transition: all var(--transition-normal);
}

.menu-toggle.active span:nth-child(1) {
  transform: translateY(9px) rotate(45deg);
}

.menu-toggle.active span:nth-child(2) {
  opacity: 0;
}

.menu-toggle.active span:nth-child(3) {
  transform: translateY(-9px) rotate(-45deg);
}

/* القائمة المتنقلة */
.mobile-menu {
  position: fixed;
  top: 0;
  right: -100%;
  width: 80%;
  max-width: 400px;
  height: 100vh;
  background-color: var(--bg-secondary);
  z-index: 100;
  padding: var(--spacing-2xl) var(--spacing-xl);
  transition: right var(--transition-normal);
  box-shadow: var(--shadow-lg);
  overflow-y: auto;
}

.mobile-menu.active {
  right: 0;
}

.mobile-nav-list {
  list-style: none;
  margin-top: var(--spacing-2xl);
}

.mobile-nav-item {
  margin-bottom: var(--spacing-lg);
}

.mobile-nav-link {
  color: var(--text-primary);
  text-decoration: none;
  font-size: var(--text-xl);
  font-weight: var(--font-medium);
  display: block;
  padding: var(--spacing-sm) 0;
  transition: all var(--transition-normal);
}

.mobile-nav-link:hover, .mobile-nav-link.active {
  color: var(--accent-primary);
  transform: translateX(var(--spacing-sm));
}

.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 90;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.menu-open .mobile-menu-overlay {
  opacity: 1;
  visibility: visible;
}

/* زر تبديل السمة */
.theme-toggle {
  position: relative;
  width: 60px;
  height: 30px;
  border-radius: 15px;
  background-color: var(--bg-tertiary);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.theme-toggle::before {
  content: '';
  position: absolute;
  top: 3px;
  left: 3px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--accent-primary);
  transition: all var(--transition-normal);
}

.theme-toggle.active::before {
  left: calc(100% - 27px);
}

/* البطاقات */
.card {
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.card-img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.card-content {
  padding: var(--spacing-lg);
}

.card-title {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.card-text {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--spacing-md);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* الأزرار */
.btn {
  display: inline-block;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--border-radius-md);
  font-weight: var(--font-medium);
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  border: none;
  outline: none;
}

.btn-primary {
  background-color: var(--accent-primary);
  color: var(--text-primary);
}

.btn-primary:hover {
  background-color: var(--accent-secondary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.btn-secondary:hover {
  background-color: var(--bg-secondary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-outline {
  background-color: transparent;
  color: var(--accent-primary);
  border: 2px solid var(--accent-primary);
}

.btn-outline:hover {
  background-color: var(--accent-primary);
  color: var(--text-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-icon {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.btn-icon i {
  font-size: var(--text-lg);
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--text-lg);
}

.btn-sm {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--text-sm);
}

.btn-rounded {
  border-radius: var(--border-radius-full);
}

/* شريط التقدم */
.progress-bar {
  width: 100%;
  height: 8px;
  background-color: var(--bg-tertiary);
  border-radius: var(--border-radius-full);
  overflow: hidden;
  margin: var(--spacing-sm) 0;
}

.progress-fill {
  height: 100%;
  background-color: var(--accent-primary);
  border-radius: var(--border-radius-full);
  transition: width 1s ease-in-out;
}

/* الشارات */
.badge {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
}

.badge-primary {
  background-color: var(--accent-primary);
  color: var(--text-primary);
}

.badge-secondary {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
}

.badge-success {
  background-color: var(--success);
  color: var(--text-primary);
}

.badge-warning {
  background-color: var(--warning);
  color: var(--text-primary);
}

.badge-error {
  background-color: var(--error);
  color: var(--text-primary);
}

/* الأيقونات الاجتماعية */
.social-icons {
  display: flex;
  gap: var(--spacing-md);
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  transition: all var(--transition-normal);
}

.social-icon:hover {
  background-color: var(--accent-primary);
  color: var(--text-primary);
  transform: translateY(-3px);
}

.social-icon i {
  font-size: var(--text-lg);
}

/* نموذج الاتصال */
.contact-form {
  display: grid;
  gap: var(--spacing-md);
}

.form-group {
  position: relative;
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

.form-input {
  width: 100%;
  padding: var(--spacing-md);
  background-color: var(--bg-tertiary);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-md);
  color: var(--text-primary);
  font-size: var(--text-md);
  transition: all var(--transition-normal);
}

.form-input:focus {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.2);
  outline: none;
}

.form-textarea {
  min-height: 150px;
  resize: vertical;
}

.form-message {
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-md);
  margin-top: var(--spacing-sm);
  font-size: var(--text-sm);
}

.form-message.success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.form-message.error {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--error);
}

.form-message.loading {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--info);
}

/* الشرائح */
.slider {
  position: relative;
  overflow: hidden;
  border-radius: var(--border-radius-lg);
}

.slides {
  display: flex;
  transition: transform 0.5s ease-in-out;
}

.slide {
  min-width: 100%;
  position: relative;
}

.slide img {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.slide-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: var(--spacing-lg);
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  color: var(--text-primary);
}

.slide-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  margin-bottom: var(--spacing-sm);
}

.slide-description {
  font-size: var(--text-md);
  margin-bottom: var(--spacing-md);
}

.slider-controls {
  position: absolute;
  bottom: var(--spacing-lg);
  right: var(--spacing-lg);
  display: flex;
  gap: var(--spacing-sm);
}

.prev-slide, .next-slide {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.prev-slide:hover, .next-slide:hover {
  background-color: var(--accent-primary);
}

.slider-dots {
  position: absolute;
  bottom: var(--spacing-lg);
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: var(--spacing-sm);
}

.slider-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.slider-dot.active {
  background-color: var(--accent-primary);
  transform: scale(1.2);
}

/* معرض الصور */
.gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--spacing-md);
}

.gallery-item {
  position: relative;
  overflow: hidden;
  border-radius: var(--border-radius-md);
  cursor: pointer;
}

.gallery-item img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: all var(--transition-normal);
}

.gallery-item:hover img {
  transform: scale(1.1);
}

.gallery-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all var(--transition-normal);
}

.gallery-item:hover .gallery-overlay {
  opacity: 1;
}

.gallery-icon {
  color: var(--text-primary);
  font-size: var(--text-3xl);
}

.gallery-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.gallery-modal.active {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  position: relative;
  max-width: 90%;
  max-height: 90vh;
}

.modal-image {
  max-width: 100%;
  max-height: 90vh;
  object-fit: contain;
}

.modal-close {
  position: absolute;
  top: -40px;
  right: 0;
  width: 30px;
  height: 30px;
  color: var(--text-primary);
  font-size: var(--text-2xl);
  cursor: pointer;
}

/* فلاتر المشاريع */
.filter-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xl);
}

.filter-btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  border: none;
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.filter-btn:hover, .filter-btn.active {
  background-color: var(--accent-primary);
  color: var(--text-primary);
}

.project-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.project-item {
  opacity: 0;
  transform: translateY(20px);
  transition: all var(--transition-normal);
}

.project-item.show {
  opacity: 1;
  transform: translateY(0);
}

/* الإحصائيات */
.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-xl);
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  color: var(--accent-primary);
  margin-bottom: var(--spacing-sm);
}

.stat-label {
  font-size: var(--text-lg);
  color: var(--text-secondary);
}

/* الشهادات */
.testimonial {
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  position: relative;
}

.testimonial-content {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  font-style: italic;
  margin-bottom: var(--spacing-lg);
  position: relative;
}

.testimonial-content::before {
  content: '"';
  font-size: 4rem;
  color: var(--accent-primary);
  opacity: 0.2;
  position: absolute;
  top: -20px;
  left: -10px;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.author-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
}

.author-info {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-size: var(--text-md);
  font-weight: var(--font-bold);
  color: var(--text-primary);
}

.author-title {
  font-size: var(--text-sm);
  color: var(--text-tertiary);
}

/* الخط الزمني */
.timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.timeline::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 100%;
  background-color: var(--bg-tertiary);
}

.timeline-item {
  position: relative;
  margin-bottom: var(--spacing-2xl);
}

.timeline-item:nth-child(odd) {
  padding-right: 50%;
}

.timeline-item:nth-child(even) {
  padding-left: 50%;
}

.timeline-dot {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: var(--accent-primary);
  z-index: 1;
}

.timeline-content {
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  position: relative;
}

.timeline-date {
  font-size: var(--text-sm);
  color: var(--accent-primary);
  margin-bottom: var(--spacing-sm);
}

.timeline-title {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.timeline-description {
  color: var(--text-secondary);
}

/* الأسئلة الشائعة */
.faq-item {
  margin-bottom: var(--spacing-md);
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.faq-question:hover {
  background-color: var(--bg-tertiary);
}

.faq-question h3 {
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.faq-toggle {
  width: 20px;
  height: 20px;
  position: relative;
}

.faq-toggle::before, .faq-toggle::after {
  content: '';
  position: absolute;
  background-color: var(--accent-primary);
  transition: all var(--transition-normal);
}

.faq-toggle::before {
  top: 50%;
  left: 0;
  width: 100%;
  height: 2px;
  transform: translateY(-50%);
}

.faq-toggle::after {
  top: 0;
  left: 50%;
  width: 2px;
  height: 100%;
  transform: translateX(-50%);
}

.faq-item.active .faq-toggle::after {
  transform: translateX(-50%) rotate(90deg);
  opacity: 0;
}

.faq-answer {
  padding: 0 var(--spacing-md);
  max-height: 0;
  overflow: hidden;
  transition: all var(--transition-normal);
}

.faq-item.active .faq-answer {
  padding: var(--spacing-md);
  max-height: 500px;
}

/* التذييل */
.footer {
  background-color: var(--bg-secondary);
  padding: var(--spacing-2xl) 0;
}

.footer-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
}

.footer-logo {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.footer-description {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
}

.footer-heading {
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.footer-links {
  list-style: none;
}

.footer-link {
  margin-bottom: var(--spacing-sm);
}

.footer-link a {
  color: var(--text-secondary);
  text-decoration: none;
  transition: all var(--transition-normal);
}

.footer-link a:hover {
  color: var(--accent-primary);
}

.footer-contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  color: var(--text-secondary);
}

.footer-contact-icon {
  color: var(--accent-primary);
  font-size: var(--text-lg);
}

.footer-bottom {
  margin-top: var(--spacing-2xl);
  padding-top: var(--spacing-lg);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.footer-copyright {
  color: var(--text-tertiary);
}

/* الاستجابة */
@media (max-width: 768px) {
  .menu-toggle {
    display: flex;
  }
  
  .nav-list {
    display: none;
  }
  
  .timeline::before {
    left: 30px;
  }
  
  .timeline-item:nth-child(odd), .timeline-item:nth-child(even) {
    padding-left: 80px;
    padding-right: 0;
  }
  
  .timeline-dot {
    left: 30px;
  }
  
  .footer-bottom {
    flex-direction: column;
    text-align: center;
  }
}
