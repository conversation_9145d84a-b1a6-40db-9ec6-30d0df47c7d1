/**
 * Force Visibility Script
 * This script ensures all sections and elements are visible regardless of animation libraries
 */

// Function to force all sections and elements to be visible
function forceVisibility() {
  console.log("Forcing visibility of all sections and elements");
  
  // Force all sections to be visible
  document.querySelectorAll('section').forEach(section => {
    section.style.opacity = '1';
    section.style.visibility = 'visible';
    section.style.display = 'block';
    section.style.transform = 'none';
  });
  
  // Force all AOS elements to be visible
  document.querySelectorAll('[data-aos]').forEach(el => {
    el.classList.add('aos-animate');
    el.style.opacity = '1';
    el.style.transform = 'none';
  });
  
  // Force hero section elements to be visible
  document.querySelectorAll('.hero-content, .hero-text, .hero-image').forEach(el => {
    el.style.opacity = '1';
    el.style.visibility = 'visible';
    el.style.transform = 'none';
  });
  
  // Force about section elements to be visible
  document.querySelectorAll('#aboutMe .about-content, #aboutMe .about-text, #aboutMe .about-image').forEach(el => {
    el.style.opacity = '1';
    el.style.visibility = 'visible';
    el.style.transform = 'none';
  });
  
  // Force skills section elements to be visible
  document.querySelectorAll('#skills .skills-content, #skills .skills-text, #skills .skills-bars').forEach(el => {
    el.style.opacity = '1';
    el.style.visibility = 'visible';
    el.style.transform = 'none';
  });
  
  console.log("Visibility forced for all elements");
}

// Run immediately
forceVisibility();

// Run again after a delay to catch any elements that might be hidden by animations
setTimeout(forceVisibility, 1000);
setTimeout(forceVisibility, 2000);

// Run on scroll events
window.addEventListener('scroll', function() {
  forceVisibility();
});

// Run on resize events
window.addEventListener('resize', function() {
  forceVisibility();
});

// Run when page is fully loaded
window.addEventListener('load', function() {
  forceVisibility();
});
