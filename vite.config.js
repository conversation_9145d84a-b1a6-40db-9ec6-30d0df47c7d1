import { defineConfig } from 'vite';
import { VitePWA } from 'vite-plugin-pwa';

export default defineConfig({
  plugins: [
    VitePWA({
      registerType: 'autoUpdate',
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg,jpg,jpeg,webp}']
      },
      includeAssets: ['favicon.ico', 'apple-touch-icon.png', 'masked-icon.svg'],
      manifest: {
        name: '<PERSON><PERSON><PERSON> Ahmed - Portfolio 2025',
        short_name: '<PERSON><PERSON><PERSON> Portfolio',
        description: 'Modern Portfolio Website with 2025 Design Trends',
        theme_color: '#9d4edd',
        background_color: '#0a0a12',
        display: 'standalone',
        icons: [
          {
            src: 'assets/svg/logo.svg',
            sizes: '192x192',
            type: 'image/svg+xml'
          },
          {
            src: 'assets/svg/logo.svg',
            sizes: '512x512',
            type: 'image/svg+xml'
          }
        ]
      }
    })
  ],
  build: {
    target: 'esnext',
    minify: 'terser',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['three', 'gsap'],
          animations: ['framer-motion', 'lenis']
        }
      }
    }
  },
  optimizeDeps: {
    include: ['three', 'gsap', 'lenis']
  },
  server: {
    port: 3000,
    open: true
  }
});
