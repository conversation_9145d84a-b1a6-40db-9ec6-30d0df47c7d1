/*
 * Soft Brutalism 2025 - Modern take on brutal design
 * Combining harsh edges with smooth interactions
 */

/* ===== Core Brutalism Elements ===== */
.brutal-card {
  background: var(--holographic-pink);
  border: 4px solid #000;
  box-shadow: 
    8px 8px 0px #ff5252,
    16px 16px 0px rgba(0, 0, 0, 0.1);
  transform: rotate(-2deg);
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  position: relative;
  overflow: hidden;
  padding: 2rem;
  margin: 1rem;
}

.brutal-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  transition: transform 0.6s ease;
}

.brutal-card:hover {
  transform: rotate(0deg) scale(1.05);
  box-shadow: 
    12px 12px 0px #ff5252,
    24px 24px 0px rgba(0, 0, 0, 0.1);
}

.brutal-card:hover::before {
  transform: rotate(45deg) translateX(100%);
}

/* ===== Brutal Buttons ===== */
.btn-brutal {
  background: var(--cyber-green);
  border: 3px solid #000;
  color: #000;
  font-weight: 900;
  font-size: 1.1rem;
  padding: 1rem 2rem;
  text-transform: uppercase;
  letter-spacing: 2px;
  box-shadow: 
    6px 6px 0px #00cc66,
    12px 12px 0px rgba(0, 0, 0, 0.2);
  transform: rotate(-1deg);
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.btn-brutal::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.btn-brutal:hover {
  transform: rotate(0deg) translateY(-3px);
  box-shadow: 
    8px 8px 0px #00cc66,
    16px 16px 0px rgba(0, 0, 0, 0.2);
}

.btn-brutal:hover::before {
  left: 100%;
}

.btn-brutal:active {
  transform: rotate(0deg) translateY(0px);
  box-shadow: 
    3px 3px 0px #00cc66,
    6px 6px 0px rgba(0, 0, 0, 0.2);
}

/* ===== Brutal Typography ===== */
.brutal-heading {
  font-family: 'Inter', sans-serif;
  font-weight: 900;
  font-size: clamp(2rem, 6vw, 4rem);
  color: #000;
  background: var(--matrix-gold);
  padding: 1rem 2rem;
  border: 4px solid #000;
  box-shadow: 
    8px 8px 0px #ffcc00,
    16px 16px 0px rgba(0, 0, 0, 0.1);
  transform: rotate(-1deg);
  text-transform: uppercase;
  letter-spacing: 3px;
  position: relative;
  display: inline-block;
  margin: 2rem 0;
}

.brutal-heading::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 8px;
  right: -8px;
  height: 4px;
  background: #000;
  transform: skew(-45deg);
}

/* ===== Brutal Input Fields ===== */
.brutal-input {
  background: #fff;
  border: 4px solid #000;
  padding: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #000;
  box-shadow: 
    6px 6px 0px #ddd,
    12px 12px 0px rgba(0, 0, 0, 0.1);
  transform: rotate(-0.5deg);
  transition: all 0.3s ease;
  outline: none;
}

.brutal-input:focus {
  transform: rotate(0deg);
  box-shadow: 
    8px 8px 0px var(--cyber-green),
    16px 16px 0px rgba(0, 0, 0, 0.1);
  border-color: var(--cyber-green);
}

.brutal-input::placeholder {
  color: #666;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* ===== Brutal Navigation ===== */
.brutal-nav {
  background: var(--neural-blue);
  border: 4px solid #000;
  box-shadow: 
    0 8px 0px #0052cc,
    0 16px 0px rgba(0, 0, 0, 0.1);
  padding: 1rem 2rem;
  transform: rotate(-0.5deg);
}

.brutal-nav-item {
  color: #fff;
  font-weight: 800;
  text-transform: uppercase;
  letter-spacing: 2px;
  padding: 0.5rem 1rem;
  border: 2px solid transparent;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.brutal-nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  transition: left 0.3s ease;
}

.brutal-nav-item:hover {
  border-color: #fff;
  transform: rotate(1deg);
}

.brutal-nav-item:hover::before {
  left: 100%;
}

/* ===== Brutal Cards Grid ===== */
.brutal-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  padding: 2rem;
}

.brutal-project-card {
  background: #fff;
  border: 5px solid #000;
  box-shadow: 
    10px 10px 0px var(--holographic-pink),
    20px 20px 0px rgba(0, 0, 0, 0.1);
  transform: rotate(-1deg);
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  overflow: hidden;
  position: relative;
}

.brutal-project-card:nth-child(even) {
  transform: rotate(1deg);
  box-shadow: 
    10px 10px 0px var(--cyber-green),
    20px 20px 0px rgba(0, 0, 0, 0.1);
}

.brutal-project-card:nth-child(3n) {
  transform: rotate(-0.5deg);
  box-shadow: 
    10px 10px 0px var(--matrix-gold),
    20px 20px 0px rgba(0, 0, 0, 0.1);
}

.brutal-project-card:hover {
  transform: rotate(0deg) scale(1.02);
  z-index: 10;
}

.brutal-project-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-bottom: 4px solid #000;
}

.brutal-project-content {
  padding: 1.5rem;
}

.brutal-project-title {
  font-weight: 900;
  font-size: 1.5rem;
  color: #000;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 1rem;
  position: relative;
}

.brutal-project-title::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 50px;
  height: 3px;
  background: var(--holographic-pink);
}

/* ===== Brutal Animations ===== */
@keyframes brutalShake {
  0%, 100% { transform: rotate(-1deg); }
  25% { transform: rotate(1deg); }
  50% { transform: rotate(-0.5deg); }
  75% { transform: rotate(0.5deg); }
}

.brutal-shake:hover {
  animation: brutalShake 0.5s ease-in-out;
}

@keyframes brutalPop {
  0% { transform: scale(1) rotate(-1deg); }
  50% { transform: scale(1.1) rotate(0deg); }
  100% { transform: scale(1) rotate(1deg); }
}

.brutal-pop:hover {
  animation: brutalPop 0.3s ease-in-out;
}

/* ===== Responsive Brutalism ===== */
@media (max-width: 768px) {
  .brutal-card {
    margin: 0.5rem;
    padding: 1rem;
    transform: rotate(-1deg);
  }
  
  .brutal-heading {
    font-size: clamp(1.5rem, 8vw, 2.5rem);
    padding: 0.5rem 1rem;
    letter-spacing: 1px;
  }
  
  .brutal-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 1rem;
  }
  
  .btn-brutal {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
    letter-spacing: 1px;
  }
}
