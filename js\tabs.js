/**
 * Tabs functionality for the Skills section
 * 2025 Portfolio
 */

document.addEventListener('DOMContentLoaded', () => {
  // Initialize tabs
  initTabs();
});

function initTabs() {
  const tabButtons = document.querySelectorAll('.tab-btn');
  const tabPanes = document.querySelectorAll('.tab-pane');
  
  if (!tabButtons.length || !tabPanes.length) return;
  
  // Add click event to each tab button
  tabButtons.forEach(button => {
    button.addEventListener('click', () => {
      // Get the tab id from data-tab attribute
      const tabId = button.getAttribute('data-tab');
      
      // Remove active class from all buttons and panes
      tabButtons.forEach(btn => btn.classList.remove('active'));
      tabPanes.forEach(pane => pane.classList.remove('active'));
      
      // Add active class to current button
      button.classList.add('active');
      
      // Find the corresponding tab pane and activate it
      const targetPane = document.getElementById(tabId);
      if (targetPane) {
        targetPane.classList.add('active');
        
        // Trigger animation for progress bars in the active tab
        const progressBars = targetPane.querySelectorAll('.progress-fill');
        progressBars.forEach(bar => {
          const width = bar.getAttribute('data-width');
          bar.style.width = width;
        });
      }
    });
  });
  
  // Activate the first tab by default
  if (tabButtons.length > 0) {
    tabButtons[0].click();
  }
}
