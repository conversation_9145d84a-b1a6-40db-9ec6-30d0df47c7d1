/**
 * AI-Inspired Interactive Effects
 * Neural networks, data visualization, and AI animations
 */

// Neural Network Animation
class NeuralNetwork {
  constructor(container) {
    this.container = container;
    this.neurons = [];
    this.connections = [];
    this.init();
  }

  init() {
    this.createNeurons();
    this.createConnections();
    this.animate();
  }

  createNeurons() {
    const layers = [4, 6, 6, 3]; // Network architecture
    const layerWidth = this.container.offsetWidth / (layers.length + 1);
    
    layers.forEach((neuronCount, layerIndex) => {
      const layerHeight = this.container.offsetHeight;
      const neuronSpacing = layerHeight / (neuronCount + 1);
      
      for (let i = 0; i < neuronCount; i++) {
        const neuron = document.createElement('div');
        neuron.className = 'neuron';
        neuron.style.position = 'absolute';
        neuron.style.left = `${(layerIndex + 1) * layerWidth}px`;
        neuron.style.top = `${(i + 1) * neuronSpacing}px`;
        neuron.style.animationDelay = `${Math.random() * 2}s`;
        
        this.container.appendChild(neuron);
        this.neurons.push({
          element: neuron,
          layer: layerIndex,
          index: i,
          x: (layerIndex + 1) * layerWidth,
          y: (i + 1) * neuronSpacing
        });
      }
    });
  }

  createConnections() {
    for (let i = 0; i < this.neurons.length; i++) {
      const currentNeuron = this.neurons[i];
      
      // Connect to neurons in the next layer
      const nextLayerNeurons = this.neurons.filter(n => n.layer === currentNeuron.layer + 1);
      
      nextLayerNeurons.forEach(nextNeuron => {
        const connection = document.createElement('div');
        connection.className = 'neural-connection';
        
        const dx = nextNeuron.x - currentNeuron.x;
        const dy = nextNeuron.y - currentNeuron.y;
        const length = Math.sqrt(dx * dx + dy * dy);
        const angle = Math.atan2(dy, dx) * 180 / Math.PI;
        
        connection.style.width = `${length}px`;
        connection.style.left = `${currentNeuron.x}px`;
        connection.style.top = `${currentNeuron.y}px`;
        connection.style.transform = `rotate(${angle}deg)`;
        connection.style.transformOrigin = '0 50%';
        connection.style.animationDelay = `${Math.random() * 3}s`;
        
        this.container.appendChild(connection);
        this.connections.push(connection);
      });
    }
  }

  animate() {
    // Add pulsing effect to random neurons
    setInterval(() => {
      const randomNeuron = this.neurons[Math.floor(Math.random() * this.neurons.length)];
      randomNeuron.element.style.animation = 'none';
      setTimeout(() => {
        randomNeuron.element.style.animation = 'neuronPulse 2s ease-in-out infinite';
      }, 10);
    }, 1000);
  }
}

// AI Text Generation Effect
class AITextGenerator {
  constructor(element, texts) {
    this.element = element;
    this.texts = texts;
    this.currentIndex = 0;
    this.currentText = '';
    this.isDeleting = false;
    this.typeSpeed = 100;
    this.deleteSpeed = 50;
    this.pauseTime = 2000;
    
    this.type();
  }

  type() {
    const fullText = this.texts[this.currentIndex];
    
    if (this.isDeleting) {
      this.currentText = fullText.substring(0, this.currentText.length - 1);
    } else {
      this.currentText = fullText.substring(0, this.currentText.length + 1);
    }
    
    this.element.innerHTML = this.currentText + '<span class="ai-cursor">|</span>';
    
    let typeSpeed = this.isDeleting ? this.deleteSpeed : this.typeSpeed;
    
    if (!this.isDeleting && this.currentText === fullText) {
      typeSpeed = this.pauseTime;
      this.isDeleting = true;
    } else if (this.isDeleting && this.currentText === '') {
      this.isDeleting = false;
      this.currentIndex = (this.currentIndex + 1) % this.texts.length;
      typeSpeed = 500;
    }
    
    setTimeout(() => this.type(), typeSpeed);
  }
}

// Data Visualization Animation
class DataVisualization {
  constructor(container) {
    this.container = container;
    this.bars = [];
    this.init();
  }

  init() {
    // Create data bars
    for (let i = 0; i < 8; i++) {
      const bar = document.createElement('div');
      bar.className = 'data-bar';
      bar.style.animationDelay = `${i * 0.1}s`;
      this.container.appendChild(bar);
      this.bars.push(bar);
    }
    
    this.animateData();
  }

  animateData() {
    setInterval(() => {
      this.bars.forEach(bar => {
        const height = Math.random() * 80 + 20;
        bar.style.height = `${height}%`;
      });
    }, 2000);
  }
}

// Quantum Field Effect
class QuantumField {
  constructor(container) {
    this.container = container;
    this.particles = [];
    this.init();
  }

  init() {
    // Create quantum particles
    for (let i = 0; i < 20; i++) {
      const particle = document.createElement('div');
      particle.className = 'quantum-particle';
      particle.style.position = 'absolute';
      particle.style.width = '4px';
      particle.style.height = '4px';
      particle.style.background = `hsl(${Math.random() * 360}, 70%, 60%)`;
      particle.style.borderRadius = '50%';
      particle.style.boxShadow = `0 0 10px currentColor`;
      
      this.container.appendChild(particle);
      this.particles.push({
        element: particle,
        x: Math.random() * this.container.offsetWidth,
        y: Math.random() * this.container.offsetHeight,
        vx: (Math.random() - 0.5) * 2,
        vy: (Math.random() - 0.5) * 2
      });
    }
    
    this.animate();
  }

  animate() {
    this.particles.forEach(particle => {
      particle.x += particle.vx;
      particle.y += particle.vy;
      
      // Bounce off edges
      if (particle.x <= 0 || particle.x >= this.container.offsetWidth) {
        particle.vx *= -1;
      }
      if (particle.y <= 0 || particle.y >= this.container.offsetHeight) {
        particle.vy *= -1;
      }
      
      particle.element.style.left = `${particle.x}px`;
      particle.element.style.top = `${particle.y}px`;
    });
    
    requestAnimationFrame(() => this.animate());
  }
}

// AI Loading Animation
class AILoader {
  constructor(container) {
    this.container = container;
    this.dots = [];
    this.init();
  }

  init() {
    for (let i = 0; i < 3; i++) {
      const dot = document.createElement('div');
      dot.className = 'ai-dot';
      dot.style.animationDelay = `${i * 0.2}s`;
      this.container.appendChild(dot);
      this.dots.push(dot);
    }
  }
}

// Machine Learning Progress
class MLProgress {
  constructor(container, duration = 5000) {
    this.container = container;
    this.duration = duration;
    this.progress = 0;
    this.init();
  }

  init() {
    const progressBar = document.createElement('div');
    progressBar.className = 'ml-progress-bar';
    this.container.appendChild(progressBar);
    
    this.animate(progressBar);
  }

  animate(progressBar) {
    const startTime = Date.now();
    
    const updateProgress = () => {
      const elapsed = Date.now() - startTime;
      this.progress = Math.min(elapsed / this.duration, 1);
      
      progressBar.style.width = `${this.progress * 100}%`;
      
      if (this.progress < 1) {
        requestAnimationFrame(updateProgress);
      } else {
        // Reset after completion
        setTimeout(() => {
          this.progress = 0;
          this.animate(progressBar);
        }, 1000);
      }
    };
    
    updateProgress();
  }
}

// Initialize AI Effects
document.addEventListener('DOMContentLoaded', () => {
  // Initialize Neural Networks
  const neuralContainers = document.querySelectorAll('.neural-network');
  neuralContainers.forEach(container => {
    new NeuralNetwork(container);
  });

  // Initialize AI Text Generators
  const aiTexts = [
    'Analyzing data patterns...',
    'Training neural networks...',
    'Optimizing algorithms...',
    'Processing user input...',
    'Generating insights...'
  ];
  
  const textGenerators = document.querySelectorAll('.ai-text-gen');
  textGenerators.forEach(element => {
    new AITextGenerator(element, aiTexts);
  });

  // Initialize Data Visualizations
  const dataVizContainers = document.querySelectorAll('.data-viz');
  dataVizContainers.forEach(container => {
    new DataVisualization(container);
  });

  // Initialize Quantum Fields
  const quantumContainers = document.querySelectorAll('.quantum-field');
  quantumContainers.forEach(container => {
    new QuantumField(container);
  });

  // Initialize AI Loaders
  const loaderContainers = document.querySelectorAll('.ai-loader');
  loaderContainers.forEach(container => {
    new AILoader(container);
  });

  // Initialize ML Progress bars
  const progressContainers = document.querySelectorAll('.ml-progress');
  progressContainers.forEach(container => {
    new MLProgress(container);
  });
});

// Export classes for external use
window.AIEffects = {
  NeuralNetwork,
  AITextGenerator,
  DataVisualization,
  QuantumField,
  AILoader,
  MLProgress
};
