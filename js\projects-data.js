/**
 * Projects Data
 * Contains detailed information about each project
 */

const projectsData = [
    {
        id: "personal-website",
        title: "Personal Website",
        category: "Web Development",
        description: "A personal portfolio website showcasing my skills, projects, and experience. Built with modern web technologies and featuring a clean, responsive design.",
        client: "Self",
        date: "2023",
        technologies: ["HTML", "CSS", "JavaScript", "Bootstrap"],
        image: "assets/svg/Youssef Ahmed.png",
        url: "https://youssef17py.github.io/YoussefAhmedShahata/",
        features: [
            "Responsive design for all devices",
            "Modern UI/UX with smooth animations",
            "Portfolio showcase with filtering",
            "Contact form with validation"
        ],
        gallery: [
            "assets/svg/Youssef Ahmed.png"
        ]
    },
    {
        id: "daily-motivator",
        title: "Daily Motivator",
        category: "Web Application",
        description: "A web application that provides daily motivational quotes and inspirational content to help users stay motivated and focused on their goals.",
        client: "Personal Project",
        date: "2023",
        technologies: ["JavaScript", "HTML", "CSS", "API Integration"],
        image: "assets/svg/محفز اليوم.png",
        url: "https://youssef17py.github.io/my/",
        features: [
            "Daily motivational quotes",
            "User-friendly interface",
            "Mobile-responsive design",
            "Social media sharing capabilities"
        ],
        gallery: [
            "assets/svg/محفز اليوم.png"
        ]
    },
    {
        id: "chat-application",
        title: "Chat Application",
        category: "Web Development",
        description: "A real-time chat application that allows users to communicate with each other instantly. Features include user authentication, message history, and real-time notifications.",
        client: "Tech Startup",
        date: "2023",
        technologies: ["React", "Firebase", "CSS", "JavaScript"],
        image: "assets/svg/ChatYoussef.png",
        url: "https://youssef18py.github.io/ChatYoussef/",
        features: [
            "Real-time messaging",
            "User authentication",
            "Message history",
            "Responsive design for all devices"
        ],
        gallery: [
            "assets/svg/ChatYoussef.png"
        ]
    },
    {
        id: "welcome-page",
        title: "Welcome Page",
        category: "Web Design",
        description: "A modern welcome page design with smooth animations and a clean user interface. Perfect for businesses looking to make a strong first impression.",
        client: "Design Agency",
        date: "2023",
        technologies: ["HTML", "CSS", "JavaScript", "GSAP"],
        image: "assets/svg/Welcome.png",
        url: "https://inquisitive-chaja-1ff2dc.netlify.app/",
        features: [
            "Modern design with animations",
            "Responsive layout",
            "Fast loading time",
            "Cross-browser compatibility"
        ],
        gallery: [
            "assets/svg/Welcome.png"
        ]
    },
    {
        id: "content-removal-tool",
        title: "Content Removal Tool",
        category: "Web Application",
        description: "A utility web application that helps users remove unwanted content from text. Useful for cleaning up data, removing duplicates, or formatting text.",
        client: "Data Management Company",
        date: "2023",
        technologies: ["JavaScript", "HTML", "CSS"],
        image: "assets/svg/Remove.png",
        url: "https://joyful-cheesecake-96c78b.netlify.app/",
        features: [
            "Text cleaning and formatting",
            "Duplicate removal",
            "Pattern matching",
            "Batch processing"
        ],
        gallery: [
            "assets/svg/Remove.png"
        ]
    },
    {
        id: "healthcare-platform",
        title: "Healthcare Platform",
        category: "Web Application",
        description: "A comprehensive healthcare platform that connects patients with healthcare providers. Features include appointment scheduling, medical records management, and telemedicine capabilities.",
        client: "Healthcare Provider",
        date: "2023",
        technologies: ["React", "Node.js", "MongoDB", "Express"],
        image: "assets/imgs/الرعاية الصحية مدعومة.png",
        url: "https://cheerful-cobbler-4db23b.netlify.app/",
        features: [
            "Appointment scheduling",
            "Medical records management",
            "Telemedicine capabilities",
            "Secure patient portal"
        ],
        gallery: [
            "assets/imgs/الرعاية الصحية مدعومة.png"
        ]
    },
    {
        id: "health-metrics-calculator",
        title: "Health Metrics Calculator",
        category: "Web Application",
        description: "A web application that calculates various health metrics such as BMI, body fat percentage, and calorie needs based on user input.",
        client: "Fitness Company",
        date: "2023",
        technologies: ["JavaScript", "HTML", "CSS", "Chart.js"],
        image: "assets/imgs/حاسبه المؤشرات الصحيه.png",
        url: "https://sahha-khareeta-jadid.lovable.app/",
        features: [
            "BMI calculation",
            "Body fat percentage estimation",
            "Calorie needs calculation",
            "Visual data representation"
        ],
        gallery: [
            "assets/imgs/حاسبه المؤشرات الصحيه.png"
        ]
    },
    {
        id: "system-dashboard",
        title: "System Dashboard",
        category: "Web Application",
        description: "A comprehensive dashboard for system monitoring and management. Provides real-time data visualization and control capabilities for system administrators.",
        client: "Tech Company",
        date: "2023",
        technologies: ["React", "Node.js", "D3.js", "WebSockets"],
        image: "assets/imgs/نظام .png",
        url: "https://sensational-lollipop-9d6ee7.netlify.app/",
        features: [
            "Real-time data visualization",
            "System monitoring",
            "User management",
            "Customizable dashboard widgets"
        ],
        gallery: [
            "assets/imgs/نظام .png"
        ]
    }
];
