/**
 * Modern Effects for 2025 Portfolio
 * Advanced animations and effects using modern libraries
 */

// Initialize AOS (Animate On Scroll) with balanced performance and reliability
function initAOS() {
  // Initialize AOS with balanced settings
  AOS.init({
    duration: 800, // Standard duration
    easing: 'ease-out',
    once: false, // Allow animations to repeat for better user experience
    mirror: true, // Enable mirror effect for better user experience
    anchorPlacement: 'top-bottom',
    disable: false, // Enable on all devices
    startEvent: 'DOMContentLoaded',
    throttleDelay: 99, // Balanced throttle delay
    debounceDelay: 50, // Balanced debounce delay
    delay: 0, // No delay by default
  });

  // Ensure critical sections (About and Skills) are always visible
  const criticalSections = document.querySelectorAll('#aboutMe, #skills');
  criticalSections.forEach(section => {
    // Make sure the section is visible
    section.style.opacity = '1';
    section.style.transform = 'translateY(0)';

    // Force AOS animations to show immediately in these sections
    const aosElements = section.querySelectorAll('[data-aos]');
    aosElements.forEach(el => {
      // Keep AOS attributes but add a class to ensure visibility
      el.classList.add('aos-animate');
    });
  });

  // Refresh AOS on scroll to ensure animations trigger correctly
  window.addEventListener('scroll', function() {
    AOS.refresh();

    // Additional check for critical sections
    criticalSections.forEach(section => {
      const rect = section.getBoundingClientRect();
      const isInViewport = (
        rect.top <= (window.innerHeight * 0.9) &&
        rect.bottom >= (window.innerHeight * 0.1)
      );

      if (isInViewport) {
        // Force all AOS elements to be visible
        const aosElements = section.querySelectorAll('[data-aos]');
        aosElements.forEach(el => {
          el.classList.add('aos-animate');
        });
      }
    });
  });

  // Refresh AOS after a short delay to ensure all elements are properly initialized
  setTimeout(() => {
    AOS.refresh();
  }, 500);
}

// Initialize GSAP ScrollTrigger with performance optimizations
function initGSAP() {
  // Check if device is mobile for performance optimization
  const isMobile = window.innerWidth <= 768;

  // Only proceed if GSAP is available
  if (typeof gsap === 'undefined') return;

  // Register ScrollTrigger plugin if available
  if (typeof ScrollTrigger !== 'undefined') {
    gsap.registerPlugin(ScrollTrigger);

    // Set defaults for better performance
    ScrollTrigger.config({
      limitCallbacks: true, // Limit callbacks for better performance
      ignoreMobileResize: true // Ignore mobile resize events for better performance
    });

    // Hero section parallax effect - only on desktop
    if (!isMobile) {
      gsap.to('.hero-image', {
        yPercent: -20,
        ease: 'none',
        scrollTrigger: {
          trigger: '.hero-section',
          start: 'top top',
          end: 'bottom top',
          scrub: 0.5 // Smoother scrub
        }
      });
    }

    // Skills progress bars animation with lazy loading
    const progressBars = gsap.utils.toArray('.progress-fill');
    if (progressBars.length > 0) {
      // Create a single ScrollTrigger for all progress bars in the same container
      const skillsSection = document.querySelector('.skills-section');
      if (skillsSection) {
        ScrollTrigger.create({
          trigger: skillsSection,
          start: 'top 80%',
          onEnter: () => {
            progressBars.forEach(fill => {
              const targetWidth = fill.getAttribute('data-width') || fill.style.width;
              gsap.fromTo(fill,
                { width: '0%' },
                {
                  width: targetWidth,
                  duration: isMobile ? 1 : 1.5,
                  ease: 'power2.out',
                  delay: 0.1
                }
              );
            });
          },
          once: true // Only trigger once for better performance
        });
      }
    }

    // Section headers animation - simplified for better performance
    gsap.utils.toArray('.section-header').forEach(header => {
      ScrollTrigger.create({
        trigger: header,
        start: 'top 85%',
        onEnter: () => {
          gsap.to(header, {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: 'power2.out'
          });
        },
        once: true // Only trigger once for better performance
      });
    });

    // Scroll progress indicator
    const progressBar = document.querySelector('.scroll-progress-bar');
    if (progressBar) {
      gsap.to(progressBar, {
        width: '100%',
        ease: 'none',
        scrollTrigger: {
          scrub: 0.2
        }
      });
    }
  }
}

// Initialize Three.js background with full visual appeal
function initThreeBackground() {
  // Check if VANTA is available and the hero section exists
  if (typeof VANTA !== 'undefined' && document.querySelector('.hero-section')) {
    // Always use the full-quality background for best visual impression
    try {
      VANTA.NET({
        el: '.hero-section',
        mouseControls: true,
        touchControls: true,
        gyroControls: false,
        minHeight: 200.00,
        minWidth: 200.00,
        scale: 1.00,
        scaleMobile: 0.75,
        color: 0x9d4edd,
        backgroundColor: 0x0a0a12,
        points: 10.00, // Increased for better visual quality
        maxDistance: 25.00, // Increased for better visual quality
        spacing: 16.00, // Decreased for better visual quality
        showDots: true,
        speed: 1.0
      });

      console.log("VANTA.NET background initialized successfully");
    } catch (error) {
      console.error("Error initializing VANTA.NET background:", error);

      // Fallback to static background if VANTA fails
      const heroSection = document.querySelector('.hero-section');
      heroSection.classList.add('static-gradient-bg');

      // Add a simple particle effect with CSS as fallback
      const particleContainer = document.createElement('div');
      particleContainer.className = 'light-particles';
      for (let i = 0; i < 30; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = `${Math.random() * 100}%`;
        particle.style.top = `${Math.random() * 100}%`;
        particle.style.animationDuration = `${Math.random() * 10 + 10}s`;
        particle.style.animationDelay = `${Math.random() * 5}s`;
        particleContainer.appendChild(particle);
      }
      heroSection.appendChild(particleContainer);
    }
  } else {
    console.warn("VANTA or hero-section not available");
  }

  // Add a direct event listener to ensure the hero section is visible
  const heroSection = document.querySelector('.hero-section');
  if (heroSection) {
    heroSection.style.opacity = '1';
    heroSection.style.visibility = 'visible';

    // Make sure all hero content is visible
    const heroContent = heroSection.querySelectorAll('.hero-content, .hero-text, .hero-image');
    heroContent.forEach(el => {
      el.style.opacity = '1';
      el.style.visibility = 'visible';
      el.style.transform = 'translateY(0)';
    });
  }
}

// Initialize enhanced scrolling with special handling for critical sections
function initLocomotiveScroll() {
  // Use native smooth scrolling with enhanced animations
  document.documentElement.style.scrollBehavior = 'smooth';

  // Special handling for About and Skills sections
  const aboutSection = document.getElementById('aboutMe');
  const skillsSection = document.getElementById('skills');

  // Force these sections to be visible initially then hide if needed
  if (aboutSection) {
    aboutSection.style.opacity = '1';
    aboutSection.style.transform = 'translateY(0)';
    aboutSection.classList.add('critical-section');
  }

  if (skillsSection) {
    skillsSection.style.opacity = '1';
    skillsSection.style.transform = 'translateY(0)';
    skillsSection.classList.add('critical-section');
  }

  // Enhanced scroll handler with priority for critical sections
  const handleScroll = () => {
    // Update header on scroll
    const header = document.querySelector('.main-header');
    if (header) {
      if (window.scrollY > 50) {
        header.classList.add('sticky');
      } else {
        header.classList.remove('sticky');
      }
    }

    // Update scroll progress indicator
    const scrollProgress = document.querySelector('.scroll-progress-bar');
    if (scrollProgress) {
      const scrollPercentage = (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100;
      scrollProgress.style.width = `${scrollPercentage}%`;
    }

    // Special handling for critical sections (About and Skills)
    const criticalSections = document.querySelectorAll('.critical-section');
    criticalSections.forEach(section => {
      const rect = section.getBoundingClientRect();
      // Use a larger detection area for critical sections
      const isInView = (
        rect.top <= window.innerHeight * 0.9 &&
        rect.bottom >= window.innerHeight * 0.1
      );

      if (isInView) {
        section.classList.add('in-viewport');

        // Ensure all children are visible
        const animatedElements = section.querySelectorAll('.manual-animate');
        animatedElements.forEach((el, index) => {
          setTimeout(() => {
            el.classList.add('visible');
          }, index * 50);
        });
      }
    });

    // Regular sections
    const regularSections = document.querySelectorAll('section:not(.critical-section)');
    regularSections.forEach(section => {
      const rect = section.getBoundingClientRect();
      const isInView = rect.top < window.innerHeight * 0.75;

      if (isInView && !section.classList.contains('revealed')) {
        section.classList.add('revealed');
      }
    });
  };

  // Add scroll event with improved throttling
  let lastScrollTime = 0;
  const scrollThreshold = 16; // ~60fps

  window.addEventListener('scroll', () => {
    const now = Date.now();
    if (now - lastScrollTime > scrollThreshold) {
      lastScrollTime = now;
      handleScroll();
    }
  });

  // Initial call to set correct states
  setTimeout(handleScroll, 100);

  // Handle anchor links with improved scrolling
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function(e) {
      e.preventDefault();
      const targetId = this.getAttribute('href');
      if (targetId === '#') return;

      const targetElement = document.querySelector(targetId);
      if (targetElement) {
        // Scroll to element with offset for header
        const headerHeight = document.querySelector('.main-header')?.offsetHeight || 0;
        const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - headerHeight;

        // Force critical sections to be visible when directly navigated to
        if (targetId === '#aboutMe' || targetId === '#skills') {
          targetElement.style.opacity = '1';
          targetElement.style.transform = 'translateY(0)';

          // Force children to be visible
          const animatedElements = targetElement.querySelectorAll('.manual-animate');
          animatedElements.forEach((el, index) => {
            setTimeout(() => {
              el.classList.add('visible');
            }, index * 50 + 300);
          });
        }

        window.scrollTo({
          top: targetPosition,
          behavior: 'smooth'
        });
      }
    });
  });
}

// Initialize Tippy.js tooltips
function initTippy() {
  if (typeof tippy !== 'undefined') {
    tippy('[data-tippy-content]', {
      theme: 'modern',
      animation: 'scale',
      duration: 200,
      arrow: true
    });
  }
}

// Initialize Swiper for testimonials
function initSwiper() {
  if (typeof Swiper !== 'undefined' && document.querySelector('.testimonials-slider')) {
    new Swiper('.testimonials-slider', {
      effect: 'coverflow',
      grabCursor: true,
      centeredSlides: true,
      slidesPerView: 'auto',
      coverflowEffect: {
        rotate: 50,
        stretch: 0,
        depth: 100,
        modifier: 1,
        slideShadows: true,
      },
      pagination: {
        el: '.swiper-pagination',
        clickable: true,
      },
      navigation: {
        nextEl: '.next-slide',
        prevEl: '.prev-slide',
      },
      autoplay: {
        delay: 5000,
        disableOnInteraction: false,
      },
    });
  }
}

// Add scroll to top functionality
function initScrollToTop() {
  const scrollToTopBtn = document.querySelector('.back-to-top');

  if (scrollToTopBtn) {
    window.addEventListener('scroll', () => {
      if (window.pageYOffset > 300) {
        scrollToTopBtn.classList.add('active');
      } else {
        scrollToTopBtn.classList.remove('active');
      }
    });

    scrollToTopBtn.addEventListener('click', (e) => {
      e.preventDefault();
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    });
  }
}

// Add scroll progress indicator
function addScrollProgressIndicator() {
  const body = document.body;
  const progressBar = document.createElement('div');
  progressBar.className = 'scroll-progress-bar';
  body.appendChild(progressBar);

  window.addEventListener('scroll', () => {
    const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
    const scrollHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
    const scrollPercentage = (scrollTop / scrollHeight) * 100;

    progressBar.style.width = scrollPercentage + '%';
  });
}

// Add theme toggle functionality
function enhanceThemeToggle() {
  const themeToggle = document.getElementById('theme-toggle');
  const html = document.documentElement;

  if (themeToggle) {
    // Add sun/moon icons
    const moonIcon = document.createElement('i');
    moonIcon.className = 'fas fa-moon theme-icon moon-icon';
    const sunIcon = document.createElement('i');
    sunIcon.className = 'fas fa-sun theme-icon sun-icon';

    themeToggle.appendChild(moonIcon);
    themeToggle.appendChild(sunIcon);

    // Update toggle state based on current theme
    const updateToggleState = () => {
      const currentTheme = html.getAttribute('data-theme');
      if (currentTheme === 'light') {
        themeToggle.classList.add('active');
      } else {
        themeToggle.classList.remove('active');
      }
    };

    updateToggleState();

    // Enhanced toggle animation
    themeToggle.addEventListener('click', () => {
      themeToggle.classList.add('animating');

      setTimeout(() => {
        const newTheme = html.getAttribute('data-theme') === 'dark' ? 'light' : 'dark';
        html.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);

        updateToggleState();
        themeToggle.classList.remove('animating');
      }, 300);
    });
  }
}

// Add microinteractions to buttons and links
function addMicrointeractions() {
  // Button hover effects
  const buttons = document.querySelectorAll('.btn');
  buttons.forEach(button => {
    button.addEventListener('mouseenter', () => {
      gsap.to(button, {
        scale: 1.05,
        duration: 0.3,
        ease: 'power2.out',
        boxShadow: '0 0 15px rgba(138, 43, 226, 0.6)'
      });
    });

    button.addEventListener('mouseleave', () => {
      gsap.to(button, {
        scale: 1,
        duration: 0.3,
        ease: 'power2.out',
        boxShadow: 'none'
      });
    });

    button.addEventListener('mousedown', () => {
      gsap.to(button, {
        scale: 0.95,
        duration: 0.1,
        ease: 'power2.out'
      });
    });

    button.addEventListener('mouseup', () => {
      gsap.to(button, {
        scale: 1.05,
        duration: 0.1,
        ease: 'power2.out'
      });
    });
  });

  // Social icon hover effects
  const socialIcons = document.querySelectorAll('.social-icon, .footer-social-icon');
  socialIcons.forEach(icon => {
    icon.addEventListener('mouseenter', () => {
      gsap.to(icon, {
        y: -5,
        duration: 0.3,
        ease: 'back.out(1.7)',
        color: 'var(--accent-primary)'
      });
    });

    icon.addEventListener('mouseleave', () => {
      gsap.to(icon, {
        y: 0,
        duration: 0.3,
        ease: 'back.out(1.7)',
        color: ''
      });
    });
  });
}

// Add magnetic effect to buttons
function addMagneticEffect() {
  const magneticElements = document.querySelectorAll('.btn-primary, .logo');

  magneticElements.forEach(elem => {
    elem.addEventListener('mousemove', (e) => {
      const rect = elem.getBoundingClientRect();
      const x = e.clientX - rect.left - rect.width / 2;
      const y = e.clientY - rect.top - rect.height / 2;

      gsap.to(elem, {
        x: x * 0.2,
        y: y * 0.2,
        duration: 0.3,
        ease: 'power2.out'
      });
    });

    elem.addEventListener('mouseleave', () => {
      gsap.to(elem, {
        x: 0,
        y: 0,
        duration: 0.5,
        ease: 'elastic.out(1, 0.3)'
      });
    });
  });
}

// Initialize all modern effects with additional reliability measures
document.addEventListener('DOMContentLoaded', () => {
  console.log("DOM Content Loaded - Initializing effects");

  // Add data-scroll-container to body for Locomotive Scroll
  document.body.setAttribute('data-scroll-container', '');

  // Add scroll progress indicator
  addScrollProgressIndicator();

  // Initialize Three.js background first for visual impact
  initThreeBackground();

  // Initialize AOS with a slight delay to ensure DOM is fully ready
  setTimeout(() => {
    initAOS();

    // Force AOS refresh after a short delay
    setTimeout(() => {
      if (typeof AOS !== 'undefined') {
        AOS.refresh();
        console.log("AOS refreshed");
      }
    }, 500);
  }, 100);

  // Initialize other effects
  initGSAP();
  initLocomotiveScroll();
  initTippy();
  initSwiper();
  initScrollToTop();
  enhanceThemeToggle();
  addMicrointeractions();
  addMagneticEffect();

  // Refresh AOS on window resize
  window.addEventListener('resize', () => {
    if (typeof AOS !== 'undefined') {
      AOS.refresh();
    }
  });

  // Force all sections to be visible after a delay
  setTimeout(() => {
    document.querySelectorAll('section').forEach(section => {
      section.style.opacity = '1';
      section.style.visibility = 'visible';

      // Force all AOS elements to be visible
      section.querySelectorAll('[data-aos]').forEach(el => {
        el.classList.add('aos-animate');
      });
    });

    console.log("All sections forced visible");
  }, 1000);
});
