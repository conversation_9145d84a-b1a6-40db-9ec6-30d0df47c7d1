/*
 * AI-Inspired Design 2025
 * Neural Networks, Algorithm-based patterns, and futuristic elements
 */

/* ===== Neural Network Visualization ===== */
.neural-network {
  position: relative;
  width: 100%;
  height: 400px;
  overflow: hidden;
  background: radial-gradient(circle at center, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
}

.neural-layer {
  position: absolute;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  height: 100%;
  width: 60px;
}

.neural-layer:nth-child(1) { left: 10%; }
.neural-layer:nth-child(2) { left: 35%; }
.neural-layer:nth-child(3) { left: 60%; }
.neural-layer:nth-child(4) { left: 85%; }

.neuron {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--cyber-green);
  box-shadow: 
    0 0 10px var(--cyber-green),
    0 0 20px var(--cyber-green),
    0 0 30px var(--cyber-green);
  animation: neuronPulse 2s ease-in-out infinite;
  position: relative;
}

.neuron::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40px;
  height: 40px;
  border: 2px solid var(--cyber-green);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: neuronRipple 2s ease-out infinite;
  opacity: 0;
}

@keyframes neuronPulse {
  0%, 100% { 
    transform: scale(1); 
    box-shadow: 0 0 10px var(--cyber-green);
  }
  50% { 
    transform: scale(1.2); 
    box-shadow: 0 0 20px var(--cyber-green), 0 0 30px var(--cyber-green);
  }
}

@keyframes neuronRipple {
  0% {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

.neural-connection {
  position: absolute;
  height: 2px;
  background: linear-gradient(90deg, var(--cyber-green), transparent, var(--neural-blue));
  animation: dataFlow 3s linear infinite;
  opacity: 0.7;
}

@keyframes dataFlow {
  0% {
    background-position: -100% 0;
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    background-position: 100% 0;
    opacity: 0;
  }
}

/* ===== Algorithm Pattern Background ===== */
.algorithm-bg {
  position: relative;
  overflow: hidden;
}

.algorithm-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(45deg, transparent 40%, rgba(102, 126, 234, 0.1) 50%, transparent 60%),
    linear-gradient(-45deg, transparent 40%, rgba(255, 0, 110, 0.1) 50%, transparent 60%);
  background-size: 40px 40px;
  animation: algorithmShift 10s linear infinite;
  z-index: -1;
}

@keyframes algorithmShift {
  0% { transform: translate(0, 0); }
  100% { transform: translate(40px, 40px); }
}

/* ===== AI Data Visualization ===== */
.data-viz {
  display: flex;
  align-items: end;
  justify-content: space-around;
  height: 100px;
  padding: 1rem;
  background: rgba(18, 18, 30, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.data-bar {
  width: 20px;
  background: linear-gradient(to top, var(--neural-blue), var(--cyber-green));
  border-radius: 4px 4px 0 0;
  animation: dataGrow 2s ease-in-out infinite;
  position: relative;
}

.data-bar:nth-child(1) { height: 20%; animation-delay: 0s; }
.data-bar:nth-child(2) { height: 60%; animation-delay: 0.2s; }
.data-bar:nth-child(3) { height: 40%; animation-delay: 0.4s; }
.data-bar:nth-child(4) { height: 80%; animation-delay: 0.6s; }
.data-bar:nth-child(5) { height: 30%; animation-delay: 0.8s; }
.data-bar:nth-child(6) { height: 70%; animation-delay: 1s; }

@keyframes dataGrow {
  0%, 100% { transform: scaleY(1); opacity: 0.7; }
  50% { transform: scaleY(1.2); opacity: 1; }
}

/* ===== AI Circuit Board Pattern ===== */
.circuit-board {
  position: relative;
  background: var(--bg-primary);
  overflow: hidden;
}

.circuit-board::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(90deg, transparent 48%, var(--cyber-green) 49%, var(--cyber-green) 51%, transparent 52%),
    linear-gradient(0deg, transparent 48%, var(--cyber-green) 49%, var(--cyber-green) 51%, transparent 52%);
  background-size: 50px 50px;
  opacity: 0.1;
  animation: circuitPulse 4s ease-in-out infinite;
}

.circuit-board::after {
  content: '';
  position: absolute;
  top: 20%;
  left: 20%;
  width: 10px;
  height: 10px;
  background: var(--cyber-green);
  border-radius: 50%;
  box-shadow: 
    100px 50px 0 var(--neural-blue),
    200px 100px 0 var(--holographic-pink),
    150px 200px 0 var(--matrix-gold),
    50px 150px 0 var(--cyber-green);
  animation: circuitNodes 6s ease-in-out infinite;
}

@keyframes circuitPulse {
  0%, 100% { opacity: 0.1; }
  50% { opacity: 0.3; }
}

@keyframes circuitNodes {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.5); }
}

/* ===== AI Loading Animation ===== */
.ai-loader {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 2rem;
}

.ai-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--cyber-green);
  animation: aiThinking 1.5s ease-in-out infinite;
}

.ai-dot:nth-child(1) { animation-delay: 0s; }
.ai-dot:nth-child(2) { animation-delay: 0.2s; }
.ai-dot:nth-child(3) { animation-delay: 0.4s; }

@keyframes aiThinking {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* ===== Quantum Computing Effect ===== */
.quantum-field {
  position: relative;
  background: radial-gradient(circle at 30% 70%, rgba(139, 92, 246, 0.2) 0%, transparent 50%),
              radial-gradient(circle at 70% 30%, rgba(6, 255, 165, 0.2) 0%, transparent 50%);
  animation: quantumFluctuation 8s ease-in-out infinite;
}

@keyframes quantumFluctuation {
  0%, 100% {
    background: radial-gradient(circle at 30% 70%, rgba(139, 92, 246, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(6, 255, 165, 0.2) 0%, transparent 50%);
  }
  25% {
    background: radial-gradient(circle at 60% 40%, rgba(255, 0, 110, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(255, 215, 0, 0.2) 0%, transparent 50%);
  }
  50% {
    background: radial-gradient(circle at 80% 20%, rgba(0, 102, 255, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 20% 80%, rgba(139, 92, 246, 0.2) 0%, transparent 50%);
  }
  75% {
    background: radial-gradient(circle at 50% 50%, rgba(6, 255, 165, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 90% 10%, rgba(255, 0, 110, 0.2) 0%, transparent 50%);
  }
}

/* ===== AI Text Generation Effect ===== */
.ai-text-gen {
  position: relative;
  overflow: hidden;
}

.ai-text-gen::after {
  content: '|';
  color: var(--cyber-green);
  animation: aiCursor 1s infinite;
  font-weight: bold;
}

@keyframes aiCursor {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* ===== Machine Learning Progress ===== */
.ml-progress {
  position: relative;
  height: 8px;
  background: rgba(18, 18, 30, 0.8);
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid rgba(102, 126, 234, 0.3);
}

.ml-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--neural-blue), var(--cyber-green), var(--holographic-pink));
  background-size: 200% 100%;
  animation: mlTraining 3s ease-in-out infinite;
  border-radius: 4px;
  position: relative;
}

.ml-progress-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: mlScan 2s linear infinite;
}

@keyframes mlTraining {
  0% { width: 0%; background-position: 0% 50%; }
  50% { width: 70%; background-position: 100% 50%; }
  100% { width: 100%; background-position: 200% 50%; }
}

@keyframes mlScan {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}
