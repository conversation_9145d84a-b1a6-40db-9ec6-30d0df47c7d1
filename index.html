<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<PERSON><PERSON><PERSON> - <PERSON> Developer and Designer Portfolio">
    <meta name="keywords" content="web developer, designer, portfolio, projects, frontend, backend">
    <title><PERSON><PERSON><PERSON> | Creative Developer</title>

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - 2025 Style -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">

    <!-- CSS Files - 2025 Enhanced -->
    <link rel="stylesheet" href="css/variables.css">
    <link rel="stylesheet" href="css/theme-system.css">
    <link rel="stylesheet" href="css/typography-2025.css">
    <link rel="stylesheet" href="css/effects-2025.css">
    <link rel="stylesheet" href="css/brutalism-soft.css">
    <link rel="stylesheet" href="css/ai-inspired.css">
    <link rel="stylesheet" href="css/holographic-effects.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/utilities.css">
    <link rel="stylesheet" href="css/backgrounds.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/modern-2025.css">
    <link rel="stylesheet" href="css/tabs-timeline.css">
    <link rel="stylesheet" href="css/performance-fixes.css">
    <link rel="stylesheet" href="style.css">

    <!-- 2025 Modern Libraries -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/locomotive-scroll@4.1.4/dist/locomotive-scroll.min.css">
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tippy.js@6/animations/scale.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tippy.js@6/themes/light.css" />

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/svg/logo.svg">
</head>
<body>
    <!-- Particles Background Canvas -->
    <canvas id="particles-canvas" class="particles-js"></canvas>

    <!-- Mobile Menu Overlay -->
    <div class="mobile-menu-overlay"></div>

    <!-- Mobile Menu -->
    <div id="mobile-menu" class="mobile-menu">
        <ul class="mobile-nav-list">
            <li class="mobile-nav-item"><a href="#home" class="mobile-nav-link">Home</a></li>
            <li class="mobile-nav-item"><a href="#aboutMe" class="mobile-nav-link">About</a></li>
            <li class="mobile-nav-item"><a href="#skills" class="mobile-nav-link">Skills</a></li>
            <li class="mobile-nav-item"><a href="#services" class="mobile-nav-link">Services</a></li>
            <li class="mobile-nav-item"><a href="#myWorks" class="mobile-nav-link">Projects</a></li>
            <li class="mobile-nav-item"><a href="#testimonials" class="mobile-nav-link">Testimonials</a></li>
            <li class="mobile-nav-item"><a href="#contactMe" class="mobile-nav-link">Contact</a></li>
        </ul>
        <div class="mobile-social-icons">
            <a href="https://www.instagram.com/_17.py/" target="_blank" class="social-icon"><i class="fab fa-instagram"></i></a>
            <a href="https://github.com/youssef17py" target="_blank" class="social-icon"><i class="fab fa-github"></i></a>
            <a href="https://twitter.com/youssef17py" target="_blank" class="social-icon"><i class="fab fa-twitter"></i></a>
            <a href="https://www.linkedin.com/in/youssef-ahmed/" target="_blank" class="social-icon"><i class="fab fa-linkedin"></i></a>
        </div>
    </div>

    <!-- Header -->
    <header class="main-header">
        <div class="container nav-container">
            <a href="#" class="logo">
                <img src="assets/svg/logo.svg" alt="Youssef Ahmed Logo">
                <span>Youssef Ahmed</span>
            </a>

            <nav class="main-nav">
                <ul class="nav-list">
                    <li><a href="#home" class="nav-link active">Home</a></li>
                    <li><a href="#aboutMe" class="nav-link">About</a></li>
                    <li><a href="#skills" class="nav-link">Skills</a></li>
                    <li><a href="#services" class="nav-link">Services</a></li>
                    <li><a href="#myWorks" class="nav-link">Projects</a></li>
                    <li><a href="#testimonials" class="nav-link">Testimonials</a></li>
                    <li><a href="#contactMe" class="nav-link">Contact</a></li>
                </ul>
            </nav>

            <!-- Advanced Theme Toggle Button -->
            <button class="theme-toggle-advanced" aria-label="Toggle theme" title="Switch between dark and light themes">
                <span class="icon icon-moon">🌙</span>
                <span class="icon icon-sun">☀️</span>
            </button>

            <!-- Mobile Menu Toggle -->
            <div id="menu-toggle" class="menu-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </header>
    <!-- Hero Section - 2025 Enhanced Style -->
    <section id="home" class="hero-section neural-bg" data-vanta-bg="true">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text" data-aos="fade-up" data-aos-duration="1000">
                    <h4 class="subtitle holographic mb-sm" data-aos="fade-up" data-aos-delay="200">Hello, I'm</h4>
                    <h1 class="hero-title heading-2025 mb-md" data-aos="fade-up" data-aos-delay="400" data-text="Youssef Ahmed">Youssef Ahmed</h1>
                    <div class="typewriter-container mb-lg" data-aos="fade-up" data-aos-delay="600">
                        <span class="typewriter-2025" data-text="AI-Powered Developer • UI/UX Innovator • Creative Technologist"></span>
                    </div>
                    <p class="hero-description body-text-2025 mb-xl glass-2025" data-aos="fade-up" data-aos-delay="800">
                        I create <span class="neon-text-2025">next-generation</span> digital experiences that blend cutting-edge technology
                        with stunning design. Let's build the <span class="holographic">future</span> together.
                    </p>
                    <div class="hero-buttons" data-aos="fade-up" data-aos-delay="1000">
                        <a href="#contactMe" class="btn btn-primary btn-lg neo-card-2025 magnetic" data-magnetic="true">
                            <span>🚀 Hire Me</span>
                        </a>
                        <a href="assets/Youssef .pdf" download class="btn btn-gold btn-lg brutal-soft">
                            <i class="fas fa-download"></i> Download CV
                        </a>
                    </div>
                </div>
                <div class="hero-image" data-aos="fade-left" data-aos-duration="1000" data-aos-delay="300">
                    <div class="image-wrapper float-animation tilt-3d holographic-border">
                        <img src="assets/svg/Hero.svg" alt="Youssef Ahmed - AI Developer" class="main-image">
                        <div class="luxury-badge neo-card-2025" data-aos="fade-up" data-aos-delay="1100">
                            <span class="badge-icon neon-text-2025"><i class="fas fa-robot"></i></span>
                            <span class="badge-text holographic">AI-Enhanced</span>
                        </div>
                        <!-- Neural Network Nodes -->
                        <div class="neural-nodes">
                            <div class="neural-node" style="top: 20%; left: 15%;"></div>
                            <div class="neural-node" style="top: 40%; left: 80%;"></div>
                            <div class="neural-node" style="top: 70%; left: 25%;"></div>
                            <div class="neural-node" style="top: 60%; left: 70%;"></div>
                        </div>
                    </div>
                    <div class="hero-shapes">
                        <div class="shape shape-1 float-animation"></div>
                        <div class="shape shape-2 float-animation"></div>
                        <div class="shape shape-3 float-animation"></div>
                    </div>
                </div>
            </div>

            <div class="scroll-indicator bounce-arrow" data-aos="fade-up" data-aos-delay="1200">
                <a href="#aboutMe" data-tippy-content="Scroll Down">
                    <i class="fas fa-chevron-down"></i>
                </a>
            </div>
        </div>

        <!-- Custom cursor elements -->
        <div class="custom-cursor"></div>
        <div class="custom-cursor-follower"></div>
    </section>
    <!-- About Section - 2025 Enhanced -->
    <section id="aboutMe" class="about-section neural-bg">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2 class="section-title heading-2025">
                    <span class="holographic">About</span>
                    <span class="neon-text-2025">Me</span>
                </h2>
                <div class="section-line holographic-border"></div>
                <p class="section-subtitle body-text-2025">Discover the mind behind the code</p>
            </div>

            <div class="about-content">
                <div class="about-image" data-aos="fade-right" data-aos-duration="1000">
                    <div class="image-frame neo-card-2025 parallax-card">
                        <img src="assets/svg/AboutMe.svg" alt="Youssef Ahmed - AI Developer" class="about-img">
                        <div class="experience-badge glass-2025">
                            <span class="exp-number holographic">3+</span>
                            <span class="exp-text neon-text-2025">Years of Innovation</span>
                        </div>
                    </div>
                    <div class="about-shapes">
                        <img src="assets/svg/lightbulb.svg" alt="AI Ideas" class="lightbulb-icon float-animation">
                        <!-- AI Circuit Pattern -->
                        <div class="neural-nodes">
                            <div class="neural-node" style="top: 10%; left: 20%;"></div>
                            <div class="neural-node" style="top: 30%; left: 75%;"></div>
                            <div class="neural-node" style="top: 80%; left: 40%;"></div>
                        </div>
                    </div>
                </div>

                <div class="about-text" data-aos="fade-left" data-aos-duration="1000">
                    <h3 class="about-greeting subheading-2025 mb-md">Hello there! I'm <span class="glitch-text" data-text="Youssef Ahmed">Youssef Ahmed</span></h3>

                    <div class="timeline" data-aos="fade-up" data-aos-delay="200">
                        <div class="timeline-item" data-aos="fade-up" data-aos-delay="300">
                            <div class="timeline-content glass-2025 tilt-3d">
                                <p class="about-paragraph body-text-2025">
                                    I'm a <span class="neon-text-2025">passionate technologist</span> who bridges the gap between
                                    <span class="holographic">artificial intelligence</span> and human creativity. With expertise in
                                    cutting-edge web technologies, I craft digital experiences that push boundaries.
                                </p>
                            </div>
                        </div>

                        <div class="timeline-item" data-aos="fade-up" data-aos-delay="400">
                            <div class="timeline-content glass-2025 tilt-3d">
                                <p class="about-paragraph body-text-2025">
                                    Currently exploring <span class="text-gradient">AI-powered development</span>, neural networks, and
                                    immersive user interfaces. I blend logic with imagination to create
                                    <span class="neon-text-2025">next-generation</span> solutions that solve real-world problems.
                                </p>
                            </div>
                        </div>

                        <div class="timeline-item" data-aos="fade-up" data-aos-delay="500">
                            <div class="timeline-content glass-2025 tilt-3d">
                                <p class="about-paragraph body-text-2025">
                                    Always <span class="holographic">innovating</span>. Always <span class="neon-text-2025">evolving</span>.
                                    Let's build the <span class="text-gradient">future</span> together! 🚀
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="about-info" data-aos="fade-up" data-aos-delay="600">
                        <div class="info-item modern-card">
                            <span class="info-label"><i class="fas fa-envelope"></i> Email:</span>
                            <span class="info-value"><EMAIL></span>
                        </div>
                        <div class="info-item modern-card">
                            <span class="info-label"><i class="fas fa-phone"></i> Phone:</span>
                            <span class="info-value">+201008100157</span>
                        </div>
                        <div class="info-item modern-card">
                            <span class="info-label"><i class="fas fa-map-marker-alt"></i> Location:</span>
                            <span class="info-value">Cairo, Egypt</span>
                        </div>
                        <div class="info-item modern-card">
                            <span class="info-label"><i class="fas fa-graduation-cap"></i> Degree:</span>
                            <span class="info-value">Computer Science</span>
                        </div>
                    </div>

                    <div class="about-buttons mt-lg" data-aos="fade-up" data-aos-delay="700">
                        <a href="#contactMe" class="btn btn-primary" data-magnetic="true">Get In Touch</a>
                        <a href="#myWorks" class="btn btn-outline">View My Work</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Section - 2025 AI Enhanced -->
    <section id="skills" class="skills-section algorithm-bg quantum-field">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2 class="brutal-heading">
                    My <span class="holographic">AI-Enhanced</span> Skills
                </h2>
                <p class="section-subtitle body-text-2025">Powered by cutting-edge technology</p>

                <!-- Neural Network Visualization -->
                <div class="neural-network" style="height: 200px; margin: 2rem 0;"></div>

                <!-- AI Text Generator -->
                <div class="ai-text-gen body-text-2025" style="text-align: center; margin: 1rem 0;"></div>
            </div>

            <div class="skills-tabs" data-aos="fade-up" data-aos-delay="200">
                <div class="tabs-nav">
                    <button class="tab-btn active" data-tab="frontend">Frontend</button>
                    <button class="tab-btn" data-tab="backend">Backend</button>
                    <button class="tab-btn" data-tab="design">Design</button>
                    <button class="tab-btn" data-tab="other">Other</button>
                </div>

                <div class="tabs-content">
                    <div class="tab-pane active" id="frontend">
                        <div class="skills-content">
                            <div class="skills-text" data-aos="fade-right" data-aos-delay="300">
                                <h3 class="skills-heading mb-md gradient-text">Frontend Development</h3>
                                <p class="skills-description mb-lg glass-effect">
                                    I specialize in creating responsive, interactive, and visually appealing user interfaces.
                                    My expertise includes modern JavaScript frameworks and CSS techniques to build seamless user experiences.
                                </p>
                            </div>

                            <div class="skills-bars" data-aos="fade-left" data-aos-delay="400">
                                <div class="skill-item neo-card-2025 circuit-board">
                                    <div class="skill-header">
                                        <h4 class="skill-title holographic">HTML & CSS</h4>
                                        <span class="skill-percentage neon-text-2025">95%</span>
                                    </div>
                                    <div class="ml-progress">
                                        <div class="ml-progress-bar" style="width: 95%"></div>
                                    </div>
                                    <div class="data-viz" style="height: 40px; margin-top: 10px;"></div>
                                </div>

                                <div class="skill-item neo-card-2025 circuit-board">
                                    <div class="skill-header">
                                        <h4 class="skill-title holographic">JavaScript</h4>
                                        <span class="skill-percentage neon-text-2025">85%</span>
                                    </div>
                                    <div class="ml-progress">
                                        <div class="ml-progress-bar" style="width: 85%"></div>
                                    </div>
                                    <div class="data-viz" style="height: 40px; margin-top: 10px;"></div>
                                </div>

                                <div class="skill-item neo-card-2025 circuit-board">
                                    <div class="skill-header">
                                        <h4 class="skill-title holographic">React.js</h4>
                                        <span class="skill-percentage neon-text-2025">80%</span>
                                    </div>
                                    <div class="ml-progress">
                                        <div class="ml-progress-bar" style="width: 80%"></div>
                                    </div>
                                    <div class="data-viz" style="height: 40px; margin-top: 10px;"></div>
                                </div>

                                <div class="skill-item neo-card-2025 circuit-board">
                                    <div class="skill-header">
                                        <h4 class="skill-title holographic">AI & Machine Learning</h4>
                                        <span class="skill-percentage neon-text-2025">75%</span>
                                    </div>
                                    <div class="ml-progress">
                                        <div class="ml-progress-bar" style="width: 75%"></div>
                                    </div>
                                    <div class="data-viz" style="height: 40px; margin-top: 10px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="tab-pane" id="backend">
                        <div class="skills-content">
                            <div class="skills-text" data-aos="fade-right" data-aos-delay="300">
                                <h3 class="skills-heading mb-md gradient-text">Backend Development</h3>
                                <p class="skills-description mb-lg glass-effect">
                                    I build robust server-side applications and APIs that power modern web applications.
                                    My focus is on creating scalable, secure, and efficient backend systems.
                                </p>
                            </div>

                            <div class="skills-bars" data-aos="fade-left" data-aos-delay="400">
                                <div class="skill-item modern-card">
                                    <div class="skill-header">
                                        <h4 class="skill-title">Node.js</h4>
                                        <span class="skill-percentage">70%</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 0%" data-width="70%"></div>
                                    </div>
                                </div>

                                <div class="skill-item modern-card">
                                    <div class="skill-header">
                                        <h4 class="skill-title">Express.js</h4>
                                        <span class="skill-percentage">75%</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 0%" data-width="75%"></div>
                                    </div>
                                </div>

                                <div class="skill-item modern-card">
                                    <div class="skill-header">
                                        <h4 class="skill-title">MongoDB</h4>
                                        <span class="skill-percentage">65%</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 0%" data-width="65%"></div>
                                    </div>
                                </div>

                                <div class="skill-item modern-card">
                                    <div class="skill-header">
                                        <h4 class="skill-title">Python</h4>
                                        <span class="skill-percentage">60%</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 0%" data-width="60%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="tab-pane" id="design">
                        <div class="skills-content">
                            <div class="skills-text" data-aos="fade-right" data-aos-delay="300">
                                <h3 class="skills-heading mb-md gradient-text">UI/UX Design</h3>
                                <p class="skills-description mb-lg glass-effect">
                                    I create intuitive and visually appealing user interfaces that enhance user experience.
                                    My design approach focuses on usability, accessibility, and aesthetic appeal.
                                </p>
                            </div>

                            <div class="skills-bars" data-aos="fade-left" data-aos-delay="400">
                                <div class="skill-item modern-card">
                                    <div class="skill-header">
                                        <h4 class="skill-title">UI/UX Design</h4>
                                        <span class="skill-percentage">75%</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 0%" data-width="75%"></div>
                                    </div>
                                </div>

                                <div class="skill-item modern-card">
                                    <div class="skill-header">
                                        <h4 class="skill-title">Figma</h4>
                                        <span class="skill-percentage">80%</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 0%" data-width="80%"></div>
                                    </div>
                                </div>

                                <div class="skill-item modern-card">
                                    <div class="skill-header">
                                        <h4 class="skill-title">Adobe XD</h4>
                                        <span class="skill-percentage">70%</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 0%" data-width="70%"></div>
                                    </div>
                                </div>

                                <div class="skill-item modern-card">
                                    <div class="skill-header">
                                        <h4 class="skill-title">Responsive Design</h4>
                                        <span class="skill-percentage">85%</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 0%" data-width="85%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="tab-pane" id="other">
                        <div class="skills-content">
                            <div class="skills-text" data-aos="fade-right" data-aos-delay="300">
                                <h3 class="skills-heading mb-md gradient-text">Other Skills</h3>
                                <p class="skills-description mb-lg glass-effect">
                                    Beyond web development, I have experience in various other technical domains
                                    that complement my primary skill set.
                                </p>
                            </div>

                            <div class="skills-bars" data-aos="fade-left" data-aos-delay="400">
                                <div class="skill-item modern-card">
                                    <div class="skill-header">
                                        <h4 class="skill-title">Game Development</h4>
                                        <span class="skill-percentage">65%</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 0%" data-width="65%"></div>
                                    </div>
                                </div>

                                <div class="skill-item modern-card">
                                    <div class="skill-header">
                                        <h4 class="skill-title">Git & Version Control</h4>
                                        <span class="skill-percentage">85%</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 0%" data-width="85%"></div>
                                    </div>
                                </div>

                                <div class="skill-item modern-card">
                                    <div class="skill-header">
                                        <h4 class="skill-title">SEO Optimization</h4>
                                        <span class="skill-percentage">70%</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 0%" data-width="70%"></div>
                                    </div>
                                </div>

                                <div class="skill-item modern-card">
                                    <div class="skill-header">
                                        <h4 class="skill-title">Mobile App Development</h4>
                                        <span class="skill-percentage">60%</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 0%" data-width="60%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="stats-container mb-xl" data-aos="fade-up" data-aos-delay="500">
                <div class="stat-item modern-card" data-aos="zoom-in" data-aos-delay="600">
                    <div class="stat-number counter" data-target="50">0</div>
                    <div class="stat-label">Projects Completed</div>
                </div>
                <div class="stat-item modern-card" data-aos="zoom-in" data-aos-delay="700">
                    <div class="stat-number counter" data-target="30">0</div>
                    <div class="stat-label">Happy Clients</div>
                </div>
                <div class="stat-item modern-card" data-aos="zoom-in" data-aos-delay="800">
                    <div class="stat-number counter" data-target="3">0</div>
                    <div class="stat-label">Years Experience</div>
                </div>
                <div class="stat-item modern-card" data-aos="zoom-in" data-aos-delay="900">
                    <div class="stat-number counter" data-target="15">0</div>
                    <div class="stat-label">Awards Received</div>
                </div>
            </div>

            <div class="tech-stack" data-aos="fade-up" data-aos-delay="1000">
                <h3 class="tech-stack-title mb-md gradient-text">Technologies I Work With</h3>
                <div class="tech-icons">
                    <div class="tech-icon" data-aos="flip-left" data-aos-delay="100"><i class="fab fa-html5"></i><span>HTML5</span></div>
                    <div class="tech-icon" data-aos="flip-left" data-aos-delay="200"><i class="fab fa-css3-alt"></i><span>CSS3</span></div>
                    <div class="tech-icon" data-aos="flip-left" data-aos-delay="300"><i class="fab fa-js"></i><span>JavaScript</span></div>
                    <div class="tech-icon" data-aos="flip-left" data-aos-delay="400"><i class="fab fa-react"></i><span>React</span></div>
                    <div class="tech-icon" data-aos="flip-left" data-aos-delay="500"><i class="fab fa-node-js"></i><span>Node.js</span></div>
                    <div class="tech-icon" data-aos="flip-left" data-aos-delay="600"><i class="fab fa-python"></i><span>Python</span></div>
                    <div class="tech-icon" data-aos="flip-left" data-aos-delay="700"><i class="fab fa-figma"></i><span>Figma</span></div>
                    <div class="tech-icon" data-aos="flip-left" data-aos-delay="800"><i class="fab fa-git-alt"></i><span>Git</span></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="services-section diagonal-lines-bg">
        <div class="container">
            <div class="section-header animate-on-scroll" data-animation="fade-in-up">
                <h2 class="section-title">
                    <span class="text-primary">My</span>
                    <span class="text-accent">Services</span>
                </h2>
                <div class="section-line"></div>
                <p class="section-subtitle">What I can do for you</p>
            </div>

            <div class="services-grid">
                <div class="service-card animate-on-scroll" data-animation="fade-in-up" data-delay="delay-100">
                    <div class="service-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <h3 class="service-title">Web Development</h3>
                    <p class="service-description">
                        I build responsive, fast-loading websites with clean code and modern technologies.
                        From simple landing pages to complex web applications.
                    </p>
                    <a href="#contactMe" class="service-link">Learn More <i class="fas fa-arrow-right"></i></a>
                </div>

                <div class="service-card animate-on-scroll" data-animation="fade-in-up" data-delay="delay-200">
                    <div class="service-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h3 class="service-title">UI/UX Design</h3>
                    <p class="service-description">
                        I create intuitive user interfaces and engaging user experiences that are both
                        beautiful and functional, focusing on user-centered design principles.
                    </p>
                    <a href="#contactMe" class="service-link">Learn More <i class="fas fa-arrow-right"></i></a>
                </div>

                <div class="service-card animate-on-scroll" data-animation="fade-in-up" data-delay="delay-300">
                    <div class="service-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 class="service-title">Mobile App Development</h3>
                    <p class="service-description">
                        I develop cross-platform mobile applications that work seamlessly on both iOS and Android
                        devices, providing a native-like experience.
                    </p>
                    <a href="#contactMe" class="service-link">Learn More <i class="fas fa-arrow-right"></i></a>
                </div>

                <div class="service-card animate-on-scroll" data-animation="fade-in-up" data-delay="delay-400">
                    <div class="service-icon">
                        <i class="fas fa-gamepad"></i>
                    </div>
                    <h3 class="service-title">Game Development</h3>
                    <p class="service-description">
                        I create engaging and interactive games for web and mobile platforms, focusing on
                        gameplay mechanics and user engagement.
                    </p>
                    <a href="#contactMe" class="service-link">Learn More <i class="fas fa-arrow-right"></i></a>
                </div>

                <div class="service-card animate-on-scroll" data-animation="fade-in-up" data-delay="delay-500">
                    <div class="service-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3 class="service-title">SEO Optimization</h3>
                    <p class="service-description">
                        I optimize websites for search engines to improve visibility and drive organic traffic,
                        implementing best practices for on-page and technical SEO.
                    </p>
                    <a href="#contactMe" class="service-link">Learn More <i class="fas fa-arrow-right"></i></a>
                </div>

                <div class="service-card animate-on-scroll" data-animation="fade-in-up" data-delay="delay-600">
                    <div class="service-icon">
                        <i class="fas fa-server"></i>
                    </div>
                    <h3 class="service-title">API Development</h3>
                    <p class="service-description">
                        I design and develop robust APIs that connect your applications with third-party services
                        or serve as the backbone for your frontend applications.
                    </p>
                    <a href="#contactMe" class="service-link">Learn More <i class="fas fa-arrow-right"></i></a>
                </div>
            </div>
        </div>
    </section>
    <!-- Projects Section - 2025 Brutal Style -->
    <section id="myWorks" class="projects-section neural-bg">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2 class="brutal-heading">
                    My <span class="neon-text-2025">Projects</span>
                </h2>
                <p class="section-subtitle body-text-2025">Cutting-edge digital experiences</p>
            </div>

            <div class="filter-buttons brutal-nav" data-aos="fade-up" data-aos-delay="200">
                <button class="filter-btn brutal-nav-item active" data-filter="all" data-tippy-content="Show all projects">All</button>
                <button class="filter-btn brutal-nav-item" data-filter="web" data-tippy-content="Web development projects">Web</button>
                <button class="filter-btn brutal-nav-item" data-filter="app" data-tippy-content="Mobile app projects">Apps</button>
                <button class="filter-btn brutal-nav-item" data-filter="ui" data-tippy-content="UI/UX design projects">Design</button>
                <button class="filter-btn brutal-nav-item" data-filter="game" data-tippy-content="Game development projects">Games</button>
            </div>

            <div class="project-grid brutal-grid" data-aos="fade-up" data-aos-delay="400">
                <div class="project-item web" data-aos="fade-up" data-aos-delay="100">
                    <div class="project-card brutal-project-card brutal-shake">
                        <img src="assets/svg/Youssef Ahmed.png" alt="Personal Website Project" class="brutal-project-image">
                        <div class="project-overlay">
                            <a href="https://youssef17py.github.io/YoussefAhmedShahata/" target="_blank" class="btn-brutal" data-tippy-content="Visit Website">
                                <i class="fas fa-external-link-alt"></i> VISIT
                            </a>
                        </div>
                        <div class="brutal-project-content">
                            <h3 class="brutal-project-title">Personal Website</h3>
                            <p class="project-category body-text-2025">Web Development</p>
                            <div class="project-tags">
                                <span class="project-tag brutal-card">HTML</span>
                                <span class="project-tag brutal-card">CSS</span>
                                <span class="project-tag brutal-card">JS</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="project-item app" data-aos="fade-up" data-aos-delay="200">
                    <div class="project-card modern-card">
                        <div class="project-image">
                            <img src="assets/svg/لقطة الشاشة 2025-04-08 052002.png" alt="Mobile App Project">
                            <div class="project-overlay">
                                <a href="#" target="_blank" class="project-link" data-tippy-content="Visit Project">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                <a href="#" class="project-details-btn" data-tippy-content="View Details">
                                    <i class="fas fa-info-circle"></i>
                                </a>
                            </div>
                        </div>
                        <div class="project-content glass-effect">
                            <h3 class="project-title gradient-text">Mobile App Interface</h3>
                            <p class="project-category">UI/UX Design</p>
                            <div class="project-tags">
                                <span class="project-tag">Figma</span>
                                <span class="project-tag">UI Design</span>
                                <span class="project-tag">Mobile</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="project-item web" data-aos="fade-up" data-aos-delay="300">
                    <div class="project-card modern-card">
                        <div class="project-image">
                            <img src="assets/svg/محفز اليوم.png" alt="Motivational App">
                            <div class="project-overlay">
                                <a href="https://youssef17py.github.io/my/" target="_blank" class="project-link" data-tippy-content="Visit Website">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                <a href="projects/project.html?id=chat-application" class="project-details-btn" data-tippy-content="View Details">
                                    <i class="fas fa-info-circle"></i>
                                </a>
                            </div>
                        </div>
                        <div class="project-content glass-effect">
                            <h3 class="project-title gradient-text">Daily Motivator</h3>
                            <p class="project-category">Web Application</p>
                            <div class="project-tags">
                                <span class="project-tag">JavaScript</span>
                                <span class="project-tag">API</span>
                                <span class="project-tag">Responsive</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="project-item ui" data-aos="fade-up" data-aos-delay="400">
                    <div class="project-card modern-card">
                        <div class="project-image">
                            <img src="assets/svg/ChatYoussef.png" alt="Chat Application">
                            <div class="project-overlay">
                                <a href="https://youssef18py.github.io/ChatYoussef/" target="_blank" class="project-link" data-tippy-content="Visit Website">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                <a href="projects/project.html?id=personal-website" class="project-details-btn" data-tippy-content="View Details">
                                    <i class="fas fa-info-circle"></i>
                                </a>
                            </div>
                        </div>
                        <div class="project-content glass-effect">
                            <h3 class="project-title gradient-text">Chat Application</h3>
                            <p class="project-category">Web Development</p>
                            <div class="project-tags">
                                <span class="project-tag">React</span>
                                <span class="project-tag">Firebase</span>
                                <span class="project-tag">Real-time</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="project-item web" data-aos="fade-up" data-aos-delay="500">
                    <div class="project-card modern-card">
                        <div class="project-image">
                            <img src="assets/svg/Welcome.png" alt="Welcome Page">
                            <div class="project-overlay">
                                <a href="https://inquisitive-chaja-1ff2dc.netlify.app/" target="_blank" class="project-link" data-tippy-content="Visit Website">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                <a href="projects/project.html?id=daily-motivator" class="project-details-btn" data-tippy-content="View Details">
                                    <i class="fas fa-info-circle"></i>
                                </a>
                            </div>
                        </div>
                        <div class="project-content glass-effect">
                            <h3 class="project-title gradient-text">Welcome Page</h3>
                            <p class="project-category">Web Design</p>
                            <div class="project-tags">
                                <span class="project-tag">HTML</span>
                                <span class="project-tag">CSS</span>
                                <span class="project-tag">Animation</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="project-item app" data-aos="fade-up" data-aos-delay="600">
                    <div class="project-card modern-card">
                        <div class="project-image">
                            <img src="assets/svg/Remove.png" alt="Content Removal App">
                            <div class="project-overlay">
                                <a href="https://joyful-cheesecake-96c78b.netlify.app/" target="_blank" class="project-link" data-tippy-content="Visit Website">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                <a href="projects/project.html?id=welcome-page" class="project-details-btn" data-tippy-content="View Details">
                                    <i class="fas fa-info-circle"></i>
                                </a>
                            </div>
                        </div>
                        <div class="project-content glass-effect">
                            <h3 class="project-title gradient-text">Content Removal Tool</h3>
                            <p class="project-category">Web Application</p>
                            <div class="project-tags">
                                <span class="project-tag">JavaScript</span>
                                <span class="project-tag">API</span>
                                <span class="project-tag">Tool</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="project-item ui" data-aos="fade-up" data-aos-delay="700">
                    <div class="project-card modern-card">
                        <div class="project-image">
                            <img src="assets/imgs/الرعاية الصحية مدعومة.png" alt="Healthcare App">
                            <div class="project-overlay">
                                <a href="https://cheerful-cobbler-4db23b.netlify.app/" target="_blank" class="project-link" data-tippy-content="Visit Website">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                <a href="projects/project.html?id=content-removal-tool" class="project-details-btn" data-tippy-content="View Details">
                                    <i class="fas fa-info-circle"></i>
                                </a>
                            </div>
                        </div>
                        <div class="project-content glass-effect">
                            <h3 class="project-title gradient-text">Healthcare Platform</h3>
                            <p class="project-category">Web Application</p>
                            <div class="project-tags">
                                <span class="project-tag">React</span>
                                <span class="project-tag">Healthcare</span>
                                <span class="project-tag">Responsive</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="project-item app" data-aos="fade-up" data-aos-delay="800">
                    <div class="project-card modern-card">
                        <div class="project-image">
                            <img src="assets/imgs/مرحبا.png" alt="Diet App">
                            <div class="project-overlay">
                                <a href="https://sahati-app-diet.lovable.app/login" target="_blank" class="project-link" data-tippy-content="Visit App">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                <a href="projects/project.html?id=healthcare-platform" class="project-details-btn" data-tippy-content="View Details">
                                    <i class="fas fa-info-circle"></i>
                                </a>
                            </div>
                        </div>
                        <div class="project-content glass-effect">
                            <h3 class="project-title gradient-text">Diet & Nutrition App</h3>
                            <p class="project-category">Mobile App</p>
                            <div class="project-tags">
                                <span class="project-tag">Flutter</span>
                                <span class="project-tag">Health</span>
                                <span class="project-tag">UI Design</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="project-item ui" data-aos="fade-up" data-aos-delay="900">
                    <div class="project-card modern-card">
                        <div class="project-image">
                            <img src="assets/imgs/حاسبه المؤشرات الصحيه.png" alt="Health Calculator">
                            <div class="project-overlay">
                                <a href="https://sahha-khareeta-jadid.lovable.app/" target="_blank" class="project-link" data-tippy-content="Visit Website">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                <a href="projects/project.html?id=health-metrics-calculator" class="project-details-btn" data-tippy-content="View Details">
                                    <i class="fas fa-info-circle"></i>
                                </a>
                            </div>
                        </div>
                        <div class="project-content glass-effect">
                            <h3 class="project-title gradient-text">Health Metrics Calculator</h3>
                            <p class="project-category">Web Application</p>
                            <div class="project-tags">
                                <span class="project-tag">JavaScript</span>
                                <span class="project-tag">Health</span>
                                <span class="project-tag">Calculator</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="project-item web" data-aos="fade-up" data-aos-delay="1000">
                    <div class="project-card modern-card">
                        <div class="project-image">
                            <img src="assets/imgs/نظام .png" alt="System Dashboard">
                            <div class="project-overlay">
                                <a href="https://sensational-lollipop-9d6ee7.netlify.app/" target="_blank" class="project-link" data-tippy-content="Visit Dashboard">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                <a href="projects/project.html?id=system-dashboard" class="project-details-btn" data-tippy-content="View Details">
                                    <i class="fas fa-info-circle"></i>
                                </a>
                            </div>
                        </div>
                        <div class="project-content glass-effect">
                            <h3 class="project-title gradient-text">System Dashboard</h3>
                            <p class="project-category">Web Application</p>
                            <div class="project-tags">
                                <span class="project-tag">React</span>
                                <span class="project-tag">Dashboard</span>
                                <span class="project-tag">Data Visualization</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-xl" data-aos="fade-up" data-aos-delay="1100">
                <a href="#contactMe" class="btn btn-primary btn-lg" data-magnetic="true">
                    <i class="fas fa-briefcase"></i> Start a Project Together
                </a>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section id="testimonials" class="testimonials-section gradient-bg">
        <div class="container">
            <div class="section-header animate-on-scroll" data-animation="fade-in-up">
                <h2 class="section-title">
                    <span class="text-primary">Client</span>
                    <span class="text-accent">Testimonials</span>
                </h2>
                <div class="section-line"></div>
                <p class="section-subtitle">What people say about my work</p>
            </div>

            <div class="testimonials-slider slider animate-on-scroll" data-animation="fade-in-up" data-auto-slide="5000">
                <div class="slides">
                    <div class="slide">
                        <div class="testimonial">
                            <div class="testimonial-content">
                                <p>"Youssef created an amazing website for my business. His attention to detail and creative approach exceeded my expectations. The site is not only beautiful but also performs exceptionally well."</p>
                            </div>
                            <div class="testimonial-author">
                                <div class="author-info">
                                    <h4 class="author-name">Ahmed Hassan</h4>
                                    <p class="author-title">CEO, TechSolutions</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="slide">
                        <div class="testimonial">
                            <div class="testimonial-content">
                                <p>"Working with Youssef was a pleasure. He understood our requirements perfectly and delivered a mobile app that our users love. His technical skills and design sense are truly impressive."</p>
                            </div>
                            <div class="testimonial-author">
                                <div class="author-info">
                                    <h4 class="author-name">Sara Ahmed</h4>
                                    <p class="author-title">Product Manager, AppWorks</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="slide">
                        <div class="testimonial">
                            <div class="testimonial-content">
                                <p>"Youssef's expertise in UI/UX design transformed our product. He created an intuitive interface that significantly improved user engagement. I highly recommend his services to anyone looking for quality work."</p>
                            </div>
                            <div class="testimonial-author">
                                <div class="author-info">
                                    <h4 class="author-name">Mohamed Ali</h4>
                                    <p class="author-title">Founder, DesignHub</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="slider-controls">
                    <div class="prev-slide"><i class="fas fa-chevron-left"></i></div>
                    <div class="next-slide"><i class="fas fa-chevron-right"></i></div>
                </div>

                <div class="slider-dots"></div>
            </div>
        </div>
    </section>
    <!-- Contact Section - 2025 Holographic -->
    <section id="contactMe" class="contact-section quantum-field holo-particles">
        <div class="container">
            <div class="section-header animate-on-scroll" data-animation="fade-in-up">
                <h2 class="brutal-heading holo-text" data-text="Let's Connect">
                    Let's <span class="holographic-advanced">Connect</span>
                </h2>
                <p class="section-subtitle body-text-2025">Ready to build the future together?</p>

                <!-- Holographic Particles -->
                <div class="holo-particles" style="height: 100px; margin: 2rem 0;"></div>
            </div>

            <div class="contact-content">
                <div class="contact-info animate-on-scroll" data-animation="fade-in-left">
                    <h3 class="contact-heading mb-lg">Get In Touch</h3>
                    <p class="contact-text mb-xl">
                        I'm always open to discussing new projects, creative ideas or opportunities to be part of your vision.
                        Feel free to contact me using your preferred method.
                    </p>

                    <div class="contact-details">
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="contact-item-content">
                                <h4>Location</h4>
                                <p>Cairo, Egypt</p>
                            </div>
                        </div>

                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="contact-item-content">
                                <h4>Email</h4>
                                <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                            </div>
                        </div>

                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-phone-alt"></i>
                            </div>
                            <div class="contact-item-content">
                                <h4>Phone</h4>
                                <p><a href="tel:+201008100157">+201008100157</a></p>
                            </div>
                        </div>

                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-globe"></i>
                            </div>
                            <div class="contact-item-content">
                                <h4>Website</h4>
                                <p><a href="https://youssef17py.github.io/YoussefAhmedShahata/" target="_blank">youssef17py.github.io</a></p>
                            </div>
                        </div>
                    </div>

                    <div class="social-links mt-xl">
                        <h4 class="social-title mb-md">Follow Me</h4>
                        <div class="social-icons">
                            <a href="https://www.linkedin.com/in/youssef-ahmed/" target="_blank" class="social-icon" title="LinkedIn">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <a href="https://github.com/youssef17py" target="_blank" class="social-icon" title="GitHub">
                                <i class="fab fa-github"></i>
                            </a>
                            <a href="https://twitter.com/youssef17py" target="_blank" class="social-icon" title="Twitter">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="https://www.instagram.com/_17.py/" target="_blank" class="social-icon" title="Instagram">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="https://www.youtube.com/channel/UCxxxxxxxx" target="_blank" class="social-icon" title="YouTube">
                                <i class="fab fa-youtube"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="contact-form-container animate-on-scroll" data-animation="fade-in-right">
                    <form id="contact-form" class="contact-form">
                        <div class="form-group">
                            <label for="name" class="form-label">Your Name</label>
                            <input type="text" id="name" class="form-input" placeholder="Enter your name" required>
                        </div>

                        <div class="form-group">
                            <label for="email" class="form-label">Your Email</label>
                            <input type="email" id="email" class="form-input" placeholder="Enter your email" required>
                        </div>

                        <div class="form-group">
                            <label for="subject" class="form-label">Subject</label>
                            <input type="text" id="subject" class="form-input" placeholder="Enter subject">
                        </div>

                        <div class="form-group">
                            <label for="message" class="form-label">Your Message</label>
                            <textarea id="message" class="form-input form-textarea" placeholder="Enter your message" required></textarea>
                        </div>

                        <button type="submit" class="btn btn-primary btn-lg w-full">
                            <i class="fas fa-paper-plane"></i> Send Message
                        </button>

                        <div class="form-message"></div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-container">
                <div class="footer-about">
                    <a href="#" class="footer-logo">Youssef Ahmed</a>
                    <p class="footer-description">
                        Creative web developer and designer focused on building beautiful and functional digital experiences.
                    </p>
                </div>

                <div class="footer-links">
                    <h3 class="footer-heading">Quick Links</h3>
                    <ul class="footer-links">
                        <li class="footer-link"><a href="#home">Home</a></li>
                        <li class="footer-link"><a href="#aboutMe">About</a></li>
                        <li class="footer-link"><a href="#skills">Skills</a></li>
                        <li class="footer-link"><a href="#services">Services</a></li>
                        <li class="footer-link"><a href="#myWorks">Projects</a></li>
                        <li class="footer-link"><a href="#contactMe">Contact</a></li>
                    </ul>
                </div>

                <div class="footer-contact">
                    <h3 class="footer-heading">Contact Info</h3>
                    <div class="footer-contact-item">
                        <span class="footer-contact-icon"><i class="fas fa-envelope"></i></span>
                        <span><EMAIL></span>
                    </div>
                    <div class="footer-contact-item">
                        <span class="footer-contact-icon"><i class="fas fa-phone-alt"></i></span>
                        <span>+201008100157</span>
                    </div>
                    <div class="footer-contact-item">
                        <span class="footer-contact-icon"><i class="fas fa-map-marker-alt"></i></span>
                        <span>Cairo, Egypt</span>
                    </div>
                </div>

                <div class="footer-newsletter">
                    <h3 class="footer-heading">Newsletter</h3>
                    <p class="footer-newsletter-text">Subscribe to my newsletter for updates on my latest projects and articles.</p>
                    <form class="footer-form">
                        <input type="email" placeholder="Enter your email" class="footer-input">
                        <button type="submit" class="footer-submit"><i class="fas fa-paper-plane"></i></button>
                    </form>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="footer-copyright">
                    <p>&copy; <span id="current-year">2025</span> Youssef Ahmed. All Rights Reserved.</p>
                </div>
                <div class="footer-social">
                    <a href="https://www.linkedin.com/in/youssef-ahmed/" target="_blank" class="footer-social-icon"><i class="fab fa-linkedin-in"></i></a>
                    <a href="https://github.com/youssef17py" target="_blank" class="footer-social-icon"><i class="fab fa-github"></i></a>
                    <a href="https://twitter.com/youssef17py" target="_blank" class="footer-social-icon"><i class="fab fa-twitter"></i></a>
                    <a href="https://www.instagram.com/_17.py/" target="_blank" class="footer-social-icon"><i class="fab fa-instagram"></i></a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button - 2025 Style -->
    <a href="#home" class="back-to-top" data-tippy-content="Back to Top">
        <i class="fas fa-arrow-up"></i>
    </a>

    <!-- Project Details Modal -->
    <div id="project-modal" class="project-modal">
        <div class="modal-content">
            <span class="modal-close">&times;</span>
            <div class="modal-body">
                <div class="modal-image">
                    <img src="" alt="Project Image" id="modal-project-image">
                </div>
                <div class="modal-details">
                    <h2 id="modal-project-title"></h2>
                    <p id="modal-project-category"></p>
                    <div class="modal-description">
                        <p id="modal-project-description"></p>
                    </div>
                    <div class="modal-info">
                        <div class="modal-info-item">
                            <h4>Client</h4>
                            <p id="modal-project-client"></p>
                        </div>
                        <div class="modal-info-item">
                            <h4>Date</h4>
                            <p id="modal-project-date"></p>
                        </div>
                        <div class="modal-info-item">
                            <h4>Technologies</h4>
                            <div id="modal-project-technologies" class="modal-technologies"></div>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <a href="#" id="modal-project-link" target="_blank" class="btn btn-primary">View Project</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Gallery Modal -->
    <div id="gallery-modal" class="gallery-modal">
        <span class="modal-close">&times;</span>
        <img class="modal-image" src="">
    </div>

    <!-- JavaScript Libraries - Direct Loading for Reliability -->
    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/locomotive-scroll@4.1.4/dist/locomotive-scroll.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.5/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.5/ScrollTrigger.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/build/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanta@latest/dist/vanta.net.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/tippy.js@6/dist/tippy-bundle.umd.js"></script>

    <!-- Custom JavaScript Files - 2025 Enhanced -->
    <script src="js/theme-system.js"></script>
    <script src="js/ai-effects.js"></script>
    <script src="js/holographic-effects.js"></script>
    <script src="js/projects-data.js"></script>
    <script src="js/animations.js"></script>
    <script src="js/main.js"></script>
    <script src="js/modern-effects.js"></script>
    <script src="js/tabs.js"></script>
    <script src="js/force-visibility.js"></script>
    <script src="js/project-modal.js"></script>
    <script src="script.js"></script>

    <!-- Preload Custom JavaScript Files -->
    <link rel="preload" href="js/animations.js" as="script">
    <link rel="preload" href="js/modern-effects.js" as="script">
    <link rel="preload" href="js/projects-data.js" as="script">
</body>
</html>