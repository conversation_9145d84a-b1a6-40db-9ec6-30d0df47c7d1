/**
 * Project Cards Functionality
 * Makes project cards clickable to go to project URL
 */

// Make project cards clickable
const initProjectCards = () => {
    const projectCards = document.querySelectorAll('.project-card');
    
    projectCards.forEach(card => {
        const projectLink = card.querySelector('.project-link');
        if (!projectLink) return;
        
        const projectUrl = projectLink.getAttribute('href');
        const projectImage = card.querySelector('.project-image');
        const projectTitle = card.querySelector('.project-title');
        
        // Make the entire card clickable except for the buttons
        card.addEventListener('click', (e) => {
            // Don't trigger if clicking on a button or link
            if (e.target.closest('.project-link') || e.target.closest('.project-details-btn')) {
                return;
            }
            
            window.open(projectUrl, '_blank');
        });
        
        // Add cursor pointer to indicate clickable
        card.style.cursor = 'pointer';
    });
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    initProjectCards();
});
