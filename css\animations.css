/* تأثيرات الظهور عند التمرير */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* تأثير الوهج للنصوص */
@keyframes textGlow {
  0% {
    text-shadow: 0 0 5px rgba(6, 182, 212, 0.5), 0 0 10px rgba(6, 182, 212, 0.3);
  }
  50% {
    text-shadow: 0 0 10px rgba(6, 182, 212, 0.8), 0 0 20px rgba(6, 182, 212, 0.5), 0 0 30px rgba(6, 182, 212, 0.3);
  }
  100% {
    text-shadow: 0 0 5px rgba(6, 182, 212, 0.5), 0 0 10px rgba(6, 182, 212, 0.3);
  }
}

/* تأثير نبض الأزرار */
@keyframes buttonPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(6, 182, 212, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(6, 182, 212, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(6, 182, 212, 0);
  }
}

/* تأثير تموج الخلفية */
@keyframes backgroundWave {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* تأثير دوران الأيقونات */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* تأثير تحرك السهم للأسفل */
@keyframes bounceArrow {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-15px);
  }
  60% {
    transform: translateY(-7px);
  }
}

/* تأثير الظهور التدريجي للصور */
@keyframes imageReveal {
  0% {
    clip-path: polygon(0 0, 0 0, 0 100%, 0 100%);
  }
  100% {
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
  }
}

/* تأثير تحرك الخلفية المتموجة */
@keyframes gradientFlow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* تأثير الكتابة */
@keyframes typing {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes blink {
  50% {
    border-color: transparent;
  }
}

/* تأثير تحرك الجزيئات */
@keyframes float {
  0% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
  100% {
    transform: translateY(0px) rotate(0deg);
  }
}

/* تطبيق الفئات للتأثيرات */
.fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

.fade-in-left {
  animation: fadeInLeft 0.6s ease-out forwards;
}

.fade-in-right {
  animation: fadeInRight 0.6s ease-out forwards;
}

.scale-in {
  animation: scaleIn 0.5s ease-out forwards;
}

.text-glow {
  animation: textGlow 3s infinite;
}

.button-pulse {
  animation: buttonPulse 2s infinite;
}

.bg-wave {
  animation: backgroundWave 15s ease infinite;
  background-size: 400% 400%;
}

.spin {
  animation: spin 10s linear infinite;
}

.bounce-arrow {
  animation: bounceArrow 2s infinite;
}

.image-reveal {
  animation: imageReveal 1.2s cubic-bezier(0.77, 0, 0.175, 1) forwards;
}

.gradient-flow {
  animation: gradientFlow 15s ease infinite;
  background-size: 300% 300%;
}

.typing-effect {
  overflow: hidden;
  white-space: nowrap;
  border-right: 3px solid var(--accent-primary);
  animation: 
    typing 3.5s steps(40, end),
    blink 0.75s step-end infinite;
}

.float {
  animation: float 6s ease-in-out infinite;
}

/* تأخير التأثيرات */
.delay-100 {
  animation-delay: 0.1s;
}

.delay-200 {
  animation-delay: 0.2s;
}

.delay-300 {
  animation-delay: 0.3s;
}

.delay-400 {
  animation-delay: 0.4s;
}

.delay-500 {
  animation-delay: 0.5s;
}

.delay-700 {
  animation-delay: 0.7s;
}

.delay-1000 {
  animation-delay: 1s;
}
