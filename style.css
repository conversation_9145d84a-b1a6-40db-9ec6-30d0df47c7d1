@import url('https://fonts.googleapis.com/css2?family=Cormorant+Garamond:ital,wght@0,300..700;1,300..700&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

/*--------------------------------------------------------------
# Base Styles
--------------------------------------------------------------*/
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-sans);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  overflow-x: hidden;
  position: relative;
}
/*--------------------------------------------------------------
# Header & Navigation
--------------------------------------------------------------*/
.main-header {
  padding: var(--spacing-md) 0;
  width: 100%;
  z-index: 100;
  transition: all var(--transition-normal);
}

.main-header.sticky {
  background-color: rgba(15, 23, 42, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-sm) 0;
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.logo img {
  height: 40px;
  transition: all var(--transition-normal);
}

.main-header.sticky .logo img {
  height: 32px;
}

.main-nav {
  display: flex;
  align-items: center;
}

.nav-list {
  display: flex;
  gap: var(--spacing-lg);
  list-style: none;
}

.nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: var(--font-medium);
  position: relative;
  padding: var(--spacing-xs) 0;
  transition: all var(--transition-normal);
}

.nav-link:hover, .nav-link.active {
  color: var(--accent-primary);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--accent-primary);
  transition: width var(--transition-normal);
}

.nav-link:hover::after, .nav-link.active::after {
  width: 100%;
}

/* Mobile Menu */
.menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: space-between;
  width: 30px;
  height: 21px;
  cursor: pointer;
  z-index: 110;
}

.menu-toggle span {
  display: block;
  width: 100%;
  height: 3px;
  background-color: var(--text-primary);
  border-radius: 3px;
  transition: all var(--transition-normal);
}

.mobile-menu {
  position: fixed;
  top: 0;
  right: -100%;
  width: 80%;
  max-width: 400px;
  height: 100vh;
  background-color: var(--bg-secondary);
  z-index: 100;
  padding: var(--spacing-2xl) var(--spacing-xl);
  transition: right var(--transition-normal);
  box-shadow: var(--shadow-lg);
  overflow-y: auto;
}

.mobile-menu.active {
  right: 0;
}

.mobile-nav-list {
  list-style: none;
  margin-top: var(--spacing-2xl);
}

.mobile-nav-item {
  margin-bottom: var(--spacing-lg);
}

.mobile-nav-link {
  color: var(--text-primary);
  text-decoration: none;
  font-size: var(--text-xl);
  font-weight: var(--font-medium);
  display: block;
  padding: var(--spacing-sm) 0;
  transition: all var(--transition-normal);
}

.mobile-nav-link:hover, .mobile-nav-link.active {
  color: var(--accent-primary);
  transform: translateX(var(--spacing-sm));
}

.mobile-social-icons {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-2xl);
}

.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 90;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.menu-open .mobile-menu-overlay {
  opacity: 1;
  visibility: visible;
}

/* Theme Toggle */
.theme-toggle {
  position: relative;
  width: 60px;
  height: 30px;
  border-radius: 15px;
  background-color: var(--bg-tertiary);
  cursor: pointer;
  transition: all var(--transition-normal);
  margin-left: var(--spacing-lg);
}

.theme-toggle::before {
  content: '';
  position: absolute;
  top: 3px;
  left: 3px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--accent-primary);
  transition: all var(--transition-normal);
}

.theme-toggle.active::before {
  left: calc(100% - 27px);
}

/*--------------------------------------------------------------
# Hero Section
--------------------------------------------------------------*/
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  padding: var(--spacing-3xl) 0;
}

.hero-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-2xl);
}

.hero-text {
  flex: 1;
}

.subtitle {
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
  margin-bottom: var(--spacing-sm);
}

.hero-title {
  font-size: var(--text-6xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  line-height: 1.2;
  margin-bottom: var(--spacing-md);
}

.typewriter-container {
  height: 40px;
  overflow: hidden;
}

.typewriter {
  font-size: var(--text-xl);
  font-weight: var(--font-medium);
  color: var(--accent-primary);
}

.hero-description {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  max-width: 600px;
  margin-bottom: var(--spacing-xl);
}

.hero-buttons {
  display: flex;
  gap: var(--spacing-md);
}

.hero-image {
  flex: 1;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-wrapper {
  position: relative;
  z-index: 1;
}

.main-image {
  max-width: 100%;
  height: auto;
}

.hero-shapes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.shape {
  position: absolute;
  border-radius: 50%;
  opacity: 0.2;
}

.shape-1 {
  width: 150px;
  height: 150px;
  background: linear-gradient(45deg, var(--accent-primary), var(--accent-tertiary));
  top: -30px;
  left: 20%;
  animation: float 8s ease-in-out infinite;
}

.shape-2 {
  width: 80px;
  height: 80px;
  background: linear-gradient(45deg, var(--accent-secondary), var(--accent-primary));
  bottom: 10%;
  right: 10%;
  animation: float 6s ease-in-out infinite 1s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, var(--accent-tertiary), var(--accent-secondary));
  bottom: 30%;
  left: 10%;
  animation: float 7s ease-in-out infinite 2s;
}

.scroll-indicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
}

.scroll-indicator a {
  color: var(--text-secondary);
  font-size: var(--text-2xl);
  transition: all var(--transition-normal);
}

.scroll-indicator a:hover {
  color: var(--accent-primary);
}
/*--------------------------------------------------------------
# Section Common Styles
--------------------------------------------------------------*/
section {
  padding: var(--spacing-3xl) 0;
  position: relative;
  overflow: hidden;
}

.section-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.section-title {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
}

.section-line {
  width: 80px;
  height: 4px;
  background: linear-gradient(to right, var(--accent-primary), var(--accent-tertiary));
  margin: var(--spacing-md) auto;
  border-radius: var(--border-radius-full);
}

.section-subtitle {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/*--------------------------------------------------------------
# About Section
--------------------------------------------------------------*/
.about-section {
  position: relative;
}

.about-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-2xl);
}

.about-image {
  flex: 1;
  position: relative;
}

.image-frame {
  position: relative;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.about-img {
  width: 100%;
  height: auto;
  display: block;
  border-radius: var(--border-radius-lg);
  transition: transform var(--transition-normal);
}

.image-frame:hover .about-img {
  transform: scale(1.05);
}

.experience-badge {
  position: absolute;
  bottom: 20px;
  right: -30px;
  background: linear-gradient(45deg, var(--accent-primary), var(--accent-tertiary));
  color: var(--text-primary);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.exp-number {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
}

.exp-text {
  font-size: var(--text-xs);
  text-align: center;
}

.about-shapes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.lightbulb-icon {
  position: absolute;
  top: -40px;
  left: 20px;
  width: 60px;
  height: 60px;
  opacity: 0.7;
}

.about-text {
  flex: 1;
}

.about-greeting {
  font-size: var(--text-2xl);
  font-weight: var(--font-semibold);
  margin-bottom: var(--spacing-md);
}

.about-paragraph {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
  line-height: 1.8;
}

.about-info {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

.info-item {
  display: flex;
  flex-direction: column;
  margin-bottom: var(--spacing-md);
}

.info-label {
  font-weight: var(--font-medium);
  color: var(--text-tertiary);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-xs);
}

.info-label i {
  color: var(--accent-primary);
}

.info-value {
  color: var(--text-primary);
}

.about-buttons {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

/*--------------------------------------------------------------
# Skills Section
--------------------------------------------------------------*/
.skills-section {
  position: relative;
}

.skills-content {
  display: flex;
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-2xl);
}

.skills-text {
  flex: 1;
}

.skills-heading {
  font-size: var(--text-2xl);
  font-weight: var(--font-semibold);
  margin-bottom: var(--spacing-md);
}

.skills-description {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  line-height: 1.8;
}

.skills-bars {
  flex: 1;
}

.skill-item {
  margin-bottom: var(--spacing-md);
}

.skill-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-xs);
}

.skill-title {
  font-size: var(--text-md);
  font-weight: var(--font-medium);
}

.skill-percentage {
  color: var(--accent-primary);
  font-weight: var(--font-semibold);
}

.tech-stack {
  margin-top: var(--spacing-2xl);
}

.tech-stack-title {
  text-align: center;
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  margin-bottom: var(--spacing-lg);
}

.tech-icons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-xl);
}

.tech-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  transition: all var(--transition-normal);
}

.tech-icon i {
  font-size: var(--text-4xl);
  color: var(--accent-primary);
}

.tech-icon span {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.tech-icon:hover {
  transform: translateY(-5px);
}

.tech-icon:hover i {
  color: var(--accent-tertiary);
}

.tech-icon:hover span {
  color: var(--text-primary);
}

/*--------------------------------------------------------------
# Services Section
--------------------------------------------------------------*/
.services-section {
  position: relative;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.service-card {
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, var(--accent-primary), var(--accent-tertiary));
  opacity: 0;
  z-index: -1;
  transition: opacity var(--transition-normal);
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
}

.service-card:hover::before {
  opacity: 0.05;
}

.service-icon {
  font-size: var(--text-4xl);
  color: var(--accent-primary);
  margin-bottom: var(--spacing-md);
}

.service-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  margin-bottom: var(--spacing-md);
}

.service-description {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  line-height: 1.6;
}

.service-link {
  color: var(--accent-primary);
  text-decoration: none;
  font-weight: var(--font-medium);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  transition: all var(--transition-normal);
}

.service-link i {
  transition: transform var(--transition-normal);
}

.service-link:hover {
  color: var(--accent-tertiary);
}

.service-link:hover i {
  transform: translateX(5px);
}

/*--------------------------------------------------------------
# Projects Section
--------------------------------------------------------------*/
.projects-section {
  position: relative;
}

.filter-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xl);
}

.filter-btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  border: none;
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all var(--transition-normal);
  font-weight: var(--font-medium);
}

.filter-btn:hover, .filter-btn.active {
  background-color: var(--accent-primary);
  color: var(--text-primary);
}

.project-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.project-card {
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.project-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
}

.project-image {
  position: relative;
  overflow: hidden;
  height: 200px;
}

.project-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.project-card:hover .project-image img {
  transform: scale(1.1);
}

.project-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.project-card:hover .project-overlay {
  opacity: 1;
}

.project-link, .project-details-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--accent-primary);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-normal);
}

.project-link:hover, .project-details-btn:hover {
  background-color: var(--text-primary);
  color: var(--accent-primary);
  transform: scale(1.1);
}

.project-content {
  padding: var(--spacing-lg);
}

.project-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  margin-bottom: var(--spacing-xs);
}

.project-category {
  color: var(--accent-primary);
  font-size: var(--text-sm);
  margin-bottom: var(--spacing-md);
}

.project-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.project-tag {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  font-size: var(--text-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
}

/*--------------------------------------------------------------
# Testimonials Section
--------------------------------------------------------------*/
.testimonials-section {
  position: relative;
}

.testimonials-slider {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

.testimonial {
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
}

.testimonial-content {
  position: relative;
  padding-left: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.testimonial-content::before {
  content: '"';
  position: absolute;
  top: -20px;
  left: -10px;
  font-size: 4rem;
  color: var(--accent-primary);
  opacity: 0.3;
  font-family: var(--font-serif);
}

.testimonial-content p {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  line-height: 1.8;
  font-style: italic;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.author-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
}

.author-name {
  font-size: var(--text-md);
  font-weight: var(--font-semibold);
  margin-bottom: var(--spacing-xs);
}

.author-title {
  font-size: var(--text-sm);
  color: var(--text-tertiary);
}

/*--------------------------------------------------------------
# Contact Section
--------------------------------------------------------------*/
.contact-section {
  position: relative;
}

.contact-content {
  display: flex;
  gap: var(--spacing-2xl);
}

.contact-info {
  flex: 1;
}

.contact-heading {
  font-size: var(--text-2xl);
  font-weight: var(--font-semibold);
  margin-bottom: var(--spacing-md);
}

.contact-text {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  line-height: 1.8;
}

.contact-details {
  margin-bottom: var(--spacing-xl);
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.contact-icon {
  width: 40px;
  height: 40px;
  background-color: var(--bg-tertiary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-primary);
  font-size: var(--text-lg);
}

.contact-item-content h4 {
  font-size: var(--text-md);
  font-weight: var(--font-semibold);
  margin-bottom: var(--spacing-xs);
}

.contact-item-content p, .contact-item-content a {
  color: var(--text-secondary);
  text-decoration: none;
  transition: color var(--transition-normal);
}

.contact-item-content a:hover {
  color: var(--accent-primary);
}

.social-links {
  margin-top: var(--spacing-xl);
}

.social-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  margin-bottom: var(--spacing-md);
}

.social-icons {
  display: flex;
  gap: var(--spacing-md);
}

.social-icon {
  width: 40px;
  height: 40px;
  background-color: var(--bg-tertiary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  font-size: var(--text-lg);
  transition: all var(--transition-normal);
}

.social-icon:hover {
  background-color: var(--accent-primary);
  color: var(--text-primary);
  transform: translateY(-5px);
}

.contact-form-container {
  flex: 1;
}

/*--------------------------------------------------------------
# Footer
--------------------------------------------------------------*/
.footer {
  background-color: var(--bg-secondary);
  padding: var(--spacing-2xl) 0 var(--spacing-md);
  position: relative;
}

.footer-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

.footer-logo {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  text-decoration: none;
  margin-bottom: var(--spacing-md);
  display: inline-block;
}

.footer-description {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  line-height: 1.6;
}

.footer-heading {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.footer-links {
  list-style: none;
}

.footer-link {
  margin-bottom: var(--spacing-sm);
}

.footer-link a {
  color: var(--text-secondary);
  text-decoration: none;
  transition: all var(--transition-normal);
}

.footer-link a:hover {
  color: var(--accent-primary);
  padding-left: var(--spacing-xs);
}

.footer-contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  color: var(--text-secondary);
}

.footer-contact-icon {
  color: var(--accent-primary);
}

.footer-newsletter-text {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
}

.footer-form {
  display: flex;
  position: relative;
}

.footer-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  padding-right: 50px;
  background-color: var(--bg-tertiary);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-md);
  color: var(--text-primary);
}

.footer-submit {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  background-color: var(--accent-primary);
  color: var(--text-primary);
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.footer-submit:hover {
  background-color: var(--accent-tertiary);
}

.footer-bottom {
  padding-top: var(--spacing-lg);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.footer-copyright {
  color: var(--text-tertiary);
}

.footer-social {
  display: flex;
  gap: var(--spacing-sm);
}

.footer-social-icon {
  width: 30px;
  height: 30px;
  background-color: var(--bg-tertiary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  font-size: var(--text-sm);
  transition: all var(--transition-normal);
}

.footer-social-icon:hover {
  background-color: var(--accent-primary);
  color: var(--text-primary);
}

/*--------------------------------------------------------------
# Back to Top Button
--------------------------------------------------------------*/
.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 40px;
  height: 40px;
  background-color: var(--accent-primary);
  color: var(--text-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  z-index: 99;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md);
}

.back-to-top.active {
  opacity: 1;
  visibility: visible;
}

.back-to-top:hover {
  background-color: var(--accent-tertiary);
  transform: translateY(-5px);
}

/* Animation Classes */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.visible {
  opacity: 1;
  transform: translateY(0);
}

/*--------------------------------------------------------------
# Project Modal
--------------------------------------------------------------*/
.project-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.project-modal.active {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  width: 90%;
  max-width: 1000px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.modal-close {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: var(--text-2xl);
  color: var(--text-primary);
  cursor: pointer;
  z-index: 10;
  transition: all var(--transition-normal);
}

.modal-close:hover {
  color: var(--accent-primary);
  transform: rotate(90deg);
}

.modal-body {
  display: flex;
  flex-wrap: wrap;
}

.modal-image {
  flex: 1;
  min-width: 300px;
}

.modal-image img {
  width: 100%;
  height: auto;
  border-radius: var(--border-radius-lg) 0 0 var(--border-radius-lg);
}

.modal-details {
  flex: 1;
  min-width: 300px;
  padding: var(--spacing-xl);
}

.modal-details h2 {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  margin-bottom: var(--spacing-xs);
}

.modal-details p {
  color: var(--accent-primary);
  font-size: var(--text-md);
  margin-bottom: var(--spacing-md);
}

.modal-description {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  line-height: 1.8;
}

.modal-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.modal-info-item h4 {
  font-size: var(--text-md);
  font-weight: var(--font-semibold);
  margin-bottom: var(--spacing-xs);
}

.modal-info-item p {
  color: var(--text-secondary);
  font-size: var(--text-sm);
  margin-bottom: 0;
}

.modal-technologies {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.modal-tech {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  font-size: var(--text-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
}

.modal-actions {
  margin-top: var(--spacing-lg);
}

/*--------------------------------------------------------------
# Responsive Design
--------------------------------------------------------------*/
@media (max-width: 1200px) {
  .container {
    max-width: var(--container-lg);
    padding-left: var(--spacing-lg);
    padding-right: var(--spacing-lg);
  }

  .hero-title {
    font-size: var(--text-5xl);
  }
}

@media (max-width: 992px) {
  .container {
    max-width: var(--container-md);
  }

  .hero-content {
    flex-direction: column;
    text-align: center;
  }

  .hero-text {
    order: 2;
  }

  .hero-image {
    order: 1;
    margin-bottom: var(--spacing-xl);
  }

  .hero-buttons {
    justify-content: center;
  }

  .about-content, .skills-content, .contact-content {
    flex-direction: column;
    gap: var(--spacing-xl);
  }

  .about-image {
    margin-bottom: var(--spacing-xl);
  }

  .experience-badge {
    right: 20px;
  }

  .services-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .container {
    max-width: var(--container-sm);
    padding-left: var(--spacing-md);
    padding-right: var(--spacing-md);
  }

  .nav-list {
    display: none;
  }

  .theme-toggle {
    display: none;
  }

  .menu-toggle {
    display: flex;
  }

  .hero-title {
    font-size: var(--text-4xl);
  }

  .hero-description {
    font-size: var(--text-md);
  }

  .section-title {
    font-size: var(--text-3xl);
  }

  .about-info {
    grid-template-columns: 1fr;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .project-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  .testimonials-slider {
    padding: 0 var(--spacing-md);
  }

  .footer-bottom {
    flex-direction: column;
    text-align: center;
  }

  .footer-social {
    margin-top: var(--spacing-md);
    justify-content: center;
  }

  .modal-body {
    flex-direction: column;
  }

  .modal-image img {
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
  }
}

@media (max-width: 576px) {
  .hero-title {
    font-size: var(--text-3xl);
  }

  .hero-buttons {
    flex-direction: column;
    width: 100%;
  }

  .hero-buttons .btn {
    width: 100%;
  }

  .section-title {
    font-size: var(--text-2xl);
  }

  .about-buttons {
    flex-direction: column;
    width: 100%;
  }

  .about-buttons .btn {
    width: 100%;
    margin-bottom: var(--spacing-sm);
  }

  .filter-buttons {
    flex-direction: column;
    width: 100%;
  }

  .filter-btn {
    width: 100%;
    margin-bottom: var(--spacing-xs);
  }

  .tech-icons {
    gap: var(--spacing-md);
  }

  .tech-icon i {
    font-size: var(--text-3xl);
  }

  .contact-form .btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .logo {
    font-size: 1.2rem;
  }
  .navbar {
    font-size: 0.9rem;
  }
  .hero h1 {
    font-size: 1.8rem;
  }
  .hero p {
    font-size: 0.85rem;
  }
  .heroBtns {
    flex-direction: column;
    gap: 0.8rem;
    justify-content: center;
    align-items: center;
  }
  .hero button {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }
  .aboutMe .aboutMeText h2 {
    font-size: 1.4rem;
  }
  .aboutMe .aboutMeText p {
    font-size: 0.85rem;
  }
  .works li {
    padding: 0.8rem;
  }
  .socialMedia a {
    padding: 0.4rem;
  }
  .socialMedia img {
    width: 18px;
    height: 18px;
  }
  footer {
    font-size: 0.7rem;
  }
}

/* Button animations */
.hireMe, .cv {
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.hireMe::before, .cv::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(240, 165, 0, 0.5), rgba(0, 173, 181, 0.5));
    z-index: -1;
    transition: all 0.4s ease-in-out;
}

.hireMe:hover::before, .cv:hover::before {
    left: 0;
}

.hireMe:hover, .cv:hover {
    color: #fff;
    transform: scale(1.1);
    transition: transform 0.3s ease-in-out;
}

/* Section animations */
.aboutMe, .works, .contactContainer {
    opacity: 0;
    transform: translateY(50px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Button hover effects */
.hireMe:hover, .cv:hover {
    box-shadow: 0 4px 15px rgba(240, 165, 0, 0.5);
}

/* Add animation to social media icons */
.socialMedia a {
    transition: transform 0.3s ease-in-out;
}

.socialMedia a:hover {
    transform: rotate(10deg) scale(1.1);
}

/* Add animation to project images */
.works li img {
    transition: transform 0.3s ease-in-out;
}

.works li:hover img {
    transform: scale(1.2) rotate(5deg);
}

/* Styling for "Web Developer" */
.hero h1:nth-of-type(2) {
    font-size: 3rem; /* Reduced font size */
    color: #00adb5; /* Changed color to match site theme */
    text-shadow: 0 0 5px rgba(0, 173, 181, 0.8), 0 0 10px rgba(0, 173, 181, 0.6);
    animation: subtleGlow 2s infinite alternate;
    position: relative;
}

/* Subtle glow effect animation */
@keyframes subtleGlow {
    0% {
        text-shadow: 0 0 5px rgba(0, 173, 181, 0.8), 0 0 10px rgba(0, 173, 181, 0.6);
    }
    100% {
        text-shadow: 0 0 10px rgba(0, 173, 181, 1), 0 0 15px rgba(0, 173, 181, 0.8);
    }
}

/* Styling for "Youssef Ahmed" to match "Web Developer" */
.hero h1:first-of-type {
    font-size: 3.5rem; /* Slightly larger than Web Developer */
    color: #00adb5; /* Match color */
    text-shadow: 0 0 5px rgba(0, 173, 181, 0.8), 0 0 10px rgba(0, 173, 181, 0.6);
    animation: subtleGlow 2s infinite alternate; /* Match animation */
}


