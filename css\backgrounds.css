/* خلفيات متحركة وتأثيرات */

/* خلفية الجزيئات */
.particles-bg {
  position: relative;
  overflow: hidden;
}

.particles-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--bg-primary);
  z-index: -2;
}

#particles-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

/* خلفية التدرج المتحرك */
.gradient-bg {
  position: relative;
  overflow: hidden;
}

.gradient-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(-45deg, 
    rgba(6, 182, 212, 0.15),
    rgba(59, 130, 246, 0.15),
    rgba(14, 165, 233, 0.15),
    rgba(6, 182, 212, 0.15));
  background-size: 400% 400%;
  animation: gradientFlow 15s ease infinite;
  z-index: -1;
}

/* خلفية الشبكة */
.grid-bg {
  position: relative;
  overflow: hidden;
}

.grid-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(6, 182, 212, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(6, 182, 212, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
  z-index: -1;
}

/* خلفية الموجات */
.wave-bg {
  position: relative;
  overflow: hidden;
}

.wave-svg {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 150px;
  z-index: -1;
}

.wave-path {
  animation: waveAnimation 10s linear infinite;
}

.wave-path-2 {
  animation: waveAnimation 15s linear infinite reverse;
}

@keyframes waveAnimation {
  0% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(-25%);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* خلفية النجوم */
.stars-bg {
  position: relative;
  overflow: hidden;
}

.stars-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.15) 1px, transparent 1px);
  background-size: 30px 30px;
  z-index: -1;
}

/* خلفية الدوائر المتحركة */
.circles-bg {
  position: relative;
  overflow: hidden;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--accent-primary), var(--accent-tertiary));
  opacity: 0.1;
  filter: blur(10px);
  animation: float 10s infinite ease-in-out;
}

.circle-1 {
  width: 300px;
  height: 300px;
  top: -150px;
  left: -150px;
  animation-delay: 0s;
}

.circle-2 {
  width: 400px;
  height: 400px;
  top: 50%;
  right: -200px;
  animation-delay: 2s;
}

.circle-3 {
  width: 200px;
  height: 200px;
  bottom: -100px;
  left: 30%;
  animation-delay: 4s;
}

.circle-4 {
  width: 250px;
  height: 250px;
  top: 20%;
  left: 10%;
  animation-delay: 6s;
}

/* خلفية الخطوط المتقاطعة */
.lines-bg {
  position: relative;
  overflow: hidden;
}

.lines-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(45deg, rgba(6, 182, 212, 0.05) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(6, 182, 212, 0.05) 25%, transparent 25%);
  background-size: 60px 60px;
  z-index: -1;
}

/* خلفية الضباب */
.fog-bg {
  position: relative;
  overflow: hidden;
}

.fog-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    linear-gradient(to right, 
      rgba(6, 182, 212, 0.05) 0%, 
      rgba(6, 182, 212, 0) 20%, 
      rgba(6, 182, 212, 0) 80%, 
      rgba(6, 182, 212, 0.05) 100%);
  filter: blur(20px);
  animation: fogAnimation 15s ease-in-out infinite;
  z-index: -1;
}

@keyframes fogAnimation {
  0% {
    transform: translateX(-25%) translateY(0%);
  }
  50% {
    transform: translateX(25%) translateY(10%);
  }
  100% {
    transform: translateX(-25%) translateY(0%);
  }
}

/* خلفية الشبكة المتدرجة */
.gradient-grid-bg {
  position: relative;
  overflow: hidden;
}

.gradient-grid-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    linear-gradient(rgba(6, 182, 212, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(6, 182, 212, 0.05) 1px, transparent 1px);
  background-size: 30px 30px;
  z-index: -2;
}

.gradient-grid-bg::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, transparent 0%, var(--bg-primary) 70%);
  z-index: -1;
}

/* خلفية الأشكال الهندسية */
.geometric-bg {
  position: relative;
  overflow: hidden;
}

.geometric-shape {
  position: absolute;
  opacity: 0.05;
  z-index: -1;
}

.shape-1 {
  width: 200px;
  height: 200px;
  border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  background-color: var(--accent-primary);
  top: 10%;
  left: 5%;
  animation: morphShape 15s ease-in-out infinite;
}

.shape-2 {
  width: 300px;
  height: 300px;
  border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  background-color: var(--accent-secondary);
  bottom: 10%;
  right: 5%;
  animation: morphShape 15s ease-in-out infinite reverse;
}

.shape-3 {
  width: 150px;
  height: 150px;
  border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
  background-color: var(--accent-tertiary);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: morphShape 20s ease-in-out infinite;
}

@keyframes morphShape {
  0% {
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  }
  25% {
    border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%;
  }
  50% {
    border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%;
  }
  75% {
    border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%;
  }
  100% {
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  }
}

/* خلفية الخطوط المتوازية */
.diagonal-lines-bg {
  position: relative;
  overflow: hidden;
}

.diagonal-lines-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: repeating-linear-gradient(
    45deg,
    rgba(6, 182, 212, 0.03),
    rgba(6, 182, 212, 0.03) 1px,
    transparent 1px,
    transparent 10px
  );
  z-index: -1;
}
