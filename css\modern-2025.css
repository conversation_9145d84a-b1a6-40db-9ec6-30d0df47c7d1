/*
 * Modern 2025 Styles
 * Advanced CSS effects and animations for the 2025 portfolio
 */

/* ===== Scroll Progress Bar ===== */
.scroll-progress-bar {
  position: fixed;
  top: 0;
  left: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary), var(--gold-accent));
  z-index: 9999;
  width: 0%;
  transition: width 0.1s;
  box-shadow: 0 0 10px rgba(157, 78, 221, 0.5);
}

/* ===== Modern Card Effects ===== */
.modern-card {
  background: rgba(18, 18, 30, 0.6);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(157, 78, 221, 0.15);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.modern-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(157, 78, 221, 0.1),
    rgba(0, 180, 216, 0.05),
    transparent
  );
  transition: all 0.8s;
}

.modern-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--gold-accent), transparent);
  opacity: 0;
  transition: opacity 0.5s ease;
}

.modern-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-neon), var(--shadow-blue);
  border-color: rgba(157, 78, 221, 0.3);
}

.modern-card:hover::before {
  left: 100%;
}

.modern-card:hover::after {
  opacity: 0.6;
}

/* ===== Glassmorphism Effects ===== */
.glass-effect {
  background: rgba(10, 10, 18, 0.6);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(157, 78, 221, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.glass-effect::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.05),
    transparent
  );
  transform: skewX(-25deg);
  transition: all 0.8s ease;
}

.glass-effect:hover::after {
  left: 150%;
}

/* ===== Neon Text Effects ===== */
.neon-text {
  color: var(--text-primary);
  text-shadow: 0 0 5px rgba(157, 78, 221, 0.5),
               0 0 10px rgba(157, 78, 221, 0.3),
               0 0 15px rgba(157, 78, 221, 0.2);
  transition: all var(--transition-normal);
  position: relative;
}

.neon-text:hover {
  text-shadow: 0 0 5px rgba(157, 78, 221, 0.7),
               0 0 10px rgba(157, 78, 221, 0.5),
               0 0 15px rgba(157, 78, 221, 0.3),
               0 0 20px rgba(157, 78, 221, 0.2);
}

/* ===== Gradient Text ===== */
.gradient-text {
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary), var(--gold-accent));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
  position: relative;
}

.gradient-text.luxury {
  background: linear-gradient(90deg, #9d4edd, #ffd700, #00b4d8);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
  position: relative;
}

.gradient-text.luxury::after {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--gold-accent), transparent);
  opacity: 0.6;
}

/* ===== Enhanced Luxury Buttons ===== */
.btn {
  position: relative;
  overflow: hidden;
  transition: all 0.4s ease;
  border-radius: var(--border-radius-md);
  font-weight: 500;
  letter-spacing: 0.5px;
}

.btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn:hover::after {
  width: 300%;
  height: 300%;
}

.btn-primary {
  background: linear-gradient(45deg, var(--accent-primary), var(--accent-tertiary));
  border: none;
  color: var(--text-primary);
  box-shadow: 0 4px 15px rgba(157, 78, 221, 0.3);
  position: relative;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--accent-primary), var(--gold-accent), var(--accent-secondary), var(--accent-tertiary));
  border-radius: calc(var(--border-radius-md) + 2px);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.btn-primary:hover {
  box-shadow: 0 6px 20px rgba(157, 78, 221, 0.5);
  transform: translateY(-2px);
}

.btn-primary:hover::before {
  opacity: 1;
}

.btn-outline {
  background: transparent;
  border: 2px solid var(--accent-primary);
  color: var(--text-primary);
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.btn-outline::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background: linear-gradient(45deg, var(--accent-primary), var(--accent-tertiary));
  transition: all 0.4s;
  z-index: -1;
}

.btn-outline:hover {
  color: var(--text-primary);
  border-color: transparent;
}

.btn-outline:hover::before {
  width: 100%;
}

.btn-gold {
  background: linear-gradient(45deg, #9d4edd, #ffd700);
  border: none;
  color: var(--text-primary);
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.btn-gold:hover {
  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5), 0 0 15px rgba(157, 78, 221, 0.3);
  transform: translateY(-2px);
}

/* ===== Enhanced Theme Toggle ===== */
.theme-toggle {
  position: relative;
  width: 60px;
  height: 30px;
  border-radius: 15px;
  background-color: var(--bg-tertiary);
  cursor: pointer;
  transition: all var(--transition-normal);
  overflow: hidden;
}

.theme-toggle::before {
  content: '';
  position: absolute;
  top: 3px;
  left: 3px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--accent-primary), var(--accent-secondary));
  transition: all var(--transition-normal);
  z-index: 2;
}

.theme-toggle.active::before {
  left: calc(100% - 27px);
}

.theme-toggle.animating::before {
  animation: togglePulse 0.3s ease-in-out;
}

.theme-icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  color: var(--text-primary);
  z-index: 1;
  transition: all var(--transition-normal);
}

.moon-icon {
  right: 10px;
  opacity: 1;
}

.sun-icon {
  left: 10px;
  opacity: 0;
}

.theme-toggle.active .moon-icon {
  opacity: 0;
}

.theme-toggle.active .sun-icon {
  opacity: 1;
}

@keyframes togglePulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* ===== Enhanced Back to Top Button ===== */
.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--accent-primary), var(--accent-tertiary));
  color: var(--text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-xl);
  box-shadow: 0 4px 15px rgba(138, 43, 226, 0.3);
  cursor: pointer;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  transition: all 0.3s ease;
  z-index: 99;
}

.back-to-top.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.back-to-top:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 20px rgba(138, 43, 226, 0.5);
}

/* ===== Enhanced Header ===== */
.main-header {
  backdrop-filter: blur(10px);
  transition: all 0.4s ease;
}

.main-header.sticky {
  background-color: rgba(31, 31, 31, 0.8);
  backdrop-filter: blur(15px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* ===== Enhanced Navigation ===== */
.nav-link {
  position: relative;
  padding: var(--spacing-xs) var(--spacing-sm);
  transition: all 0.3s ease;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.nav-link:hover, .nav-link.active {
  color: var(--accent-primary);
}

.nav-link:hover::after, .nav-link.active::after {
  width: 80%;
}

/* ===== Enhanced Luxury Hero Section ===== */
.hero-section {
  position: relative;
  min-height: 100vh;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(157, 78, 221, 0.1), transparent 50%),
              radial-gradient(circle at bottom left, rgba(0, 180, 216, 0.1), transparent 50%);
  z-index: 0;
}

.hero-content {
  position: relative;
  z-index: 2;
}

.hero-title {
  font-size: clamp(2.5rem, 8vw, 4.5rem);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: var(--spacing-md);
  background: linear-gradient(90deg, var(--text-primary), var(--accent-primary), var(--gold-accent));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  position: relative;
}

.hero-title::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 40%;
  height: 1px;
  background: linear-gradient(90deg, var(--accent-primary), var(--gold-accent), transparent);
}

.typewriter {
  position: relative;
  font-weight: 600;
  color: var(--accent-secondary);
  text-shadow: 0 0 10px rgba(0, 180, 216, 0.3);
}

.typewriter::after {
  content: '|';
  position: absolute;
  right: -8px;
  animation: blink 0.7s infinite;
}

.hero-description {
  position: relative;
  padding: var(--spacing-md) var(--spacing-lg);
  border-left: 2px solid var(--gold-accent);
  max-width: 600px;
}

/* Luxury Badge */
.luxury-badge {
  position: absolute;
  top: 20px;
  right: 20px;
  background: linear-gradient(45deg, var(--accent-primary), var(--accent-tertiary));
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: var(--border-radius-full);
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: var(--shadow-gold), var(--shadow-neon);
  transform: rotate(10deg);
  z-index: 10;
}

.badge-icon {
  color: var(--gold-accent);
  font-size: 16px;
}

.badge-text {
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* ===== Enhanced Luxury Project Cards ===== */
.project-card {
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  transition: all 0.4s ease;
  background: var(--bg-secondary);
  box-shadow: var(--shadow-md);
  position: relative;
}

.project-card::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(45deg, var(--accent-primary), var(--gold-accent), var(--accent-secondary), var(--accent-tertiary));
  border-radius: calc(var(--border-radius-lg) + 1px);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.project-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-neon), var(--shadow-blue);
}

.project-card:hover::before {
  opacity: 1;
}

.project-image {
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.project-image img {
  transition: transform 0.8s ease;
  cursor: pointer;
}

.project-image:active img,
.project-image.clicked img {
  filter: brightness(0.95) blur(1px);
  transform: scale(0.98);
}

.project-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, transparent 70%, rgba(10, 10, 18, 0.8));
  opacity: 0;
  transition: opacity 0.4s ease;
}

.project-card:hover .project-image::after {
  opacity: 1;
}

.project-image img {
  transition: transform 0.8s ease;
}

.project-card:hover .project-image img {
  transform: scale(1.1) rotate(1deg);
}

.project-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, rgba(157, 78, 221, 0.8), rgba(0, 180, 216, 0.4));
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  opacity: 0;
  transition: all 0.5s ease;
  backdrop-filter: blur(3px);
}

.project-card:hover .project-overlay {
  opacity: 1;
}

.project-link, .project-details-btn {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.95);
  color: var(--accent-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-lg);
  transform: translateY(20px) scale(0.8);
  opacity: 0;
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.project-card:hover .project-link,
.project-card:hover .project-details-btn {
  transform: translateY(0) scale(1);
  opacity: 1;
}

.project-link:hover, .project-details-btn:hover {
  background: var(--text-primary);
  transform: scale(1.15);
  box-shadow: 0 0 15px rgba(157, 78, 221, 0.5);
}

.project-card:hover .project-link {
  transition-delay: 0.1s;
}

.project-card:hover .project-details-btn {
  transition-delay: 0.2s;
}

.project-tag {
  background: rgba(157, 78, 221, 0.1);
  color: var(--accent-primary);
  border-radius: var(--border-radius-full);
  padding: 4px 12px;
  font-size: var(--text-xs);
  transition: all 0.3s ease;
  border: 1px solid rgba(157, 78, 221, 0.2);
}

.project-tag:hover {
  background: rgba(157, 78, 221, 0.2);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 2px 8px rgba(157, 78, 221, 0.3);
  border-color: rgba(157, 78, 221, 0.3);
}

/* ===== Enhanced Testimonials ===== */
.testimonial {
  background: rgba(31, 31, 31, 0.5);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(138, 43, 226, 0.1);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  transition: all 0.4s ease;
}

.testimonial:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-neon);
  border-color: rgba(138, 43, 226, 0.3);
}

.testimonial-content {
  position: relative;
}

.testimonial-content::before {
  content: '"';
  position: absolute;
  top: -40px;
  left: -10px;
  font-size: 100px;
  font-family: var(--font-serif);
  color: rgba(138, 43, 226, 0.1);
  line-height: 1;
}

.author-avatar {
  border: 3px solid var(--accent-primary);
  box-shadow: 0 0 15px rgba(138, 43, 226, 0.3);
}

/* ===== Enhanced Contact Form ===== */
.form-input {
  background: rgba(31, 31, 31, 0.5);
  border: 1px solid rgba(138, 43, 226, 0.2);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md);
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(138, 43, 226, 0.1);
  outline: none;
}

.form-input::placeholder {
  color: var(--text-tertiary);
}

/* ===== Media Queries ===== */
@media (max-width: 992px) {
  .hero-content {
    flex-direction: column;
  }

  .about-content, .skills-content {
    flex-direction: column;
  }

  .project-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .project-grid {
    grid-template-columns: 1fr;
  }

  .contact-content {
    flex-direction: column;
  }

  .back-to-top {
    bottom: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
  }
}

/* ===== Light Theme Overrides ===== */
[data-theme="light"] {
  .modern-card, .testimonial {
    background: rgba(255, 255, 255, 0.7);
    border-color: rgba(138, 43, 226, 0.1);
  }

  .glass-effect {
    background: rgba(255, 255, 255, 0.7);
  }

  .form-input {
    background: rgba(255, 255, 255, 0.7);
  }

  .main-header.sticky {
    background-color: rgba(248, 250, 252, 0.8);
  }
}

/* ===== Animations for AOS ===== */
[data-aos="reveal-up"] {
  transform: translateY(50px);
  opacity: 0;
  transition-property: transform, opacity;

  &.aos-animate {
    transform: translateY(0);
    opacity: 1;
  }
}

[data-aos="reveal-down"] {
  transform: translateY(-50px);
  opacity: 0;
  transition-property: transform, opacity;

  &.aos-animate {
    transform: translateY(0);
    opacity: 1;
  }
}

[data-aos="reveal-left"] {
  transform: translateX(50px);
  opacity: 0;
  transition-property: transform, opacity;

  &.aos-animate {
    transform: translateX(0);
    opacity: 1;
  }
}

[data-aos="reveal-right"] {
  transform: translateX(-50px);
  opacity: 0;
  transition-property: transform, opacity;

  &.aos-animate {
    transform: translateX(0);
    opacity: 1;
  }
}

[data-aos="zoom-in-slight"] {
  transform: scale(0.95);
  opacity: 0;
  transition-property: transform, opacity;

  &.aos-animate {
    transform: scale(1);
    opacity: 1;
  }
}

/* ===== Locomotive Scroll Styles ===== */
html.has-scroll-smooth {
  overflow: hidden;
}

html.has-scroll-dragging {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.has-scroll-smooth body {
  overflow: hidden;
}

.has-scroll-smooth [data-scroll-container] {
  min-height: 100vh;
}

[data-scroll-direction="horizontal"] [data-scroll-container] {
  height: 100vh;
  display: inline-block;
  white-space: nowrap;
}

[data-scroll-direction="horizontal"] [data-scroll-section] {
  display: inline-block;
  vertical-align: top;
  white-space: nowrap;
  height: 100%;
}

.c-scrollbar {
  position: absolute;
  right: 0;
  top: 0;
  width: 11px;
  height: 100%;
  transform-origin: center right;
  transition: transform 0.3s, opacity 0.3s;
  opacity: 0;
}

.c-scrollbar:hover {
  transform: scaleX(1.45);
}

.c-scrollbar:hover, .has-scroll-scrolling .c-scrollbar, .has-scroll-dragging .c-scrollbar {
  opacity: 1;
}

.c-scrollbar_thumb {
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--accent-primary);
  opacity: 0.5;
  width: 7px;
  border-radius: 10px;
  margin: 2px;
  cursor: -webkit-grab;
  cursor: grab;
}

.has-scroll-dragging .c-scrollbar_thumb {
  cursor: -webkit-grabbing;
  cursor: grabbing;
}

/* ===== Custom Cursor ===== */
.custom-cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(138, 43, 226, 0.3);
  pointer-events: none;
  z-index: 9999;
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s, background 0.3s;
  mix-blend-mode: difference;
}

.custom-cursor-follower {
  position: fixed;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 1px solid rgba(138, 43, 226, 0.5);
  pointer-events: none;
  z-index: 9998;
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s, border-color 0.3s;
  mix-blend-mode: difference;
}

.cursor-grow {
  width: 50px;
  height: 50px;
  background: rgba(138, 43, 226, 0.1);
  mix-blend-mode: normal;
}

.cursor-follower-grow {
  width: 70px;
  height: 70px;
  border-color: rgba(138, 43, 226, 0.2);
  mix-blend-mode: normal;
}
