/*
 * Holographic Effects 2025
 * Advanced holographic animations and color-shifting effects
 */

/* ===== CSS Houdini Properties ===== */
@property --holo-angle {
  syntax: '<angle>';
  initial-value: 0deg;
  inherits: false;
}

@property --holo-shift {
  syntax: '<percentage>';
  initial-value: 0%;
  inherits: false;
}

@property --holo-intensity {
  syntax: '<number>';
  initial-value: 1;
  inherits: false;
}

/* ===== Base Holographic Effect ===== */
.holographic-advanced {
  position: relative;
  background: linear-gradient(
    var(--holo-angle),
    #ff006e 0%,
    #8338ec 12.5%,
    #3a86ff 25%,
    #06ffa5 37.5%,
    #ffbe0b 50%,
    #ff006e 62.5%,
    #8338ec 75%,
    #3a86ff 87.5%,
    #06ffa5 100%
  );
  background-size: 200% 200%;
  animation: 
    holographicRotate 8s linear infinite,
    holographicShift 4s ease-in-out infinite alternate;
  
  /* Holographic overlay */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      45deg,
      transparent 30%,
      rgba(255, 255, 255, 0.1) 50%,
      transparent 70%
    );
    background-size: 20px 20px;
    animation: holographicScan 3s linear infinite;
    pointer-events: none;
  }
  
  /* Prismatic effect */
  &::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(
      var(--holo-angle),
      rgba(255, 0, 110, 0.3),
      rgba(131, 56, 236, 0.3),
      rgba(58, 134, 255, 0.3),
      rgba(6, 255, 165, 0.3),
      rgba(255, 190, 11, 0.3)
    );
    filter: blur(2px);
    z-index: -1;
    animation: holographicPrism 6s ease-in-out infinite;
  }
}

@keyframes holographicRotate {
  0% { --holo-angle: 0deg; }
  100% { --holo-angle: 360deg; }
}

@keyframes holographicShift {
  0% { 
    background-position: 0% 50%;
    --holo-shift: 0%;
  }
  100% { 
    background-position: 100% 50%;
    --holo-shift: 100%;
  }
}

@keyframes holographicScan {
  0% { transform: translateX(-100%) skewX(-15deg); }
  100% { transform: translateX(200%) skewX(-15deg); }
}

@keyframes holographicPrism {
  0%, 100% { 
    opacity: 0.3;
    filter: blur(2px) hue-rotate(0deg);
  }
  50% { 
    opacity: 0.6;
    filter: blur(1px) hue-rotate(180deg);
  }
}

/* ===== Holographic Text ===== */
.holo-text {
  background: linear-gradient(
    90deg,
    #ff006e 0%,
    #8338ec 20%,
    #3a86ff 40%,
    #06ffa5 60%,
    #ffbe0b 80%,
    #ff006e 100%
  );
  background-size: 200% auto;
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  animation: holographicTextFlow 3s linear infinite;
  
  /* Text shadow for depth */
  filter: drop-shadow(0 0 10px rgba(255, 0, 110, 0.5))
          drop-shadow(2px 2px 0px rgba(58, 134, 255, 0.3))
          drop-shadow(-2px -2px 0px rgba(6, 255, 165, 0.3));
  
  position: relative;
}

.holo-text::before {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  background: linear-gradient(
    90deg,
    rgba(255, 0, 110, 0.7),
    rgba(131, 56, 236, 0.7),
    rgba(58, 134, 255, 0.7)
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  animation: holographicGlitch 4s ease-in-out infinite;
  z-index: -1;
}

@keyframes holographicTextFlow {
  0% { background-position: 0% center; }
  100% { background-position: 200% center; }
}

@keyframes holographicGlitch {
  0%, 90%, 100% { 
    transform: translate(0);
    opacity: 0;
  }
  92%, 98% { 
    transform: translate(2px, -1px);
    opacity: 0.7;
  }
  94%, 96% { 
    transform: translate(-1px, 2px);
    opacity: 0.5;
  }
}

/* ===== Holographic Cards ===== */
.holo-card {
  position: relative;
  background: rgba(18, 18, 30, 0.9);
  border-radius: 16px;
  padding: 2rem;
  overflow: hidden;
  transition: transform 0.3s ease;
  
  /* Holographic border */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 2px;
    background: linear-gradient(
      45deg,
      #ff006e,
      #8338ec,
      #3a86ff,
      #06ffa5,
      #ffbe0b,
      #ff006e
    );
    background-size: 300% 300%;
    border-radius: 16px;
    animation: holographicBorder 4s linear infinite;
    z-index: -1;
  }
  
  /* Inner content background */
  &::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    right: 2px;
    bottom: 2px;
    background: var(--bg-primary);
    border-radius: 14px;
    z-index: -1;
  }
}

.holo-card:hover {
  transform: translateY(-5px) rotateX(5deg) rotateY(5deg);
}

@keyframes holographicBorder {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* ===== Holographic Buttons ===== */
.btn-holo {
  position: relative;
  background: transparent;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s ease;
  
  /* Holographic background */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      45deg,
      #ff006e 0%,
      #8338ec 25%,
      #3a86ff 50%,
      #06ffa5 75%,
      #ffbe0b 100%
    );
    background-size: 200% 200%;
    animation: holographicButtonBg 3s ease-in-out infinite;
    z-index: -1;
  }
  
  /* Shine effect */
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.4),
      transparent
    );
    transition: left 0.5s ease;
  }
}

.btn-holo:hover::after {
  left: 100%;
}

.btn-holo:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 10px 25px rgba(255, 0, 110, 0.3),
    0 0 50px rgba(58, 134, 255, 0.2);
}

@keyframes holographicButtonBg {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* ===== Holographic Loading ===== */
.holo-loader {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: conic-gradient(
    from 0deg,
    #ff006e,
    #8338ec,
    #3a86ff,
    #06ffa5,
    #ffbe0b,
    #ff006e
  );
  animation: holographicSpin 2s linear infinite;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 5px;
    left: 5px;
    right: 5px;
    bottom: 5px;
    background: var(--bg-primary);
    border-radius: 50%;
  }
}

@keyframes holographicSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== Holographic Particles ===== */
.holo-particles {
  position: relative;
  overflow: hidden;
}

.holo-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: #ff006e;
  border-radius: 50%;
  animation: holographicFloat 6s ease-in-out infinite;
  
  &:nth-child(1) { 
    left: 10%; 
    animation-delay: 0s;
    background: #ff006e;
  }
  &:nth-child(2) { 
    left: 20%; 
    animation-delay: 1s;
    background: #8338ec;
  }
  &:nth-child(3) { 
    left: 30%; 
    animation-delay: 2s;
    background: #3a86ff;
  }
  &:nth-child(4) { 
    left: 40%; 
    animation-delay: 3s;
    background: #06ffa5;
  }
  &:nth-child(5) { 
    left: 50%; 
    animation-delay: 4s;
    background: #ffbe0b;
  }
}

@keyframes holographicFloat {
  0%, 100% {
    transform: translateY(100vh) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
    transform: translateY(90vh) scale(1);
  }
  90% {
    opacity: 1;
    transform: translateY(10vh) scale(1);
  }
  100% {
    transform: translateY(0) scale(0);
    opacity: 0;
  }
}

/* ===== Responsive Holographic Effects ===== */
@media (max-width: 768px) {
  .holographic-advanced {
    animation-duration: 6s, 3s;
  }
  
  .holo-text {
    animation-duration: 2s;
  }
  
  .holo-card {
    padding: 1rem;
  }
  
  .btn-holo {
    padding: 0.8rem 1.5rem;
  }
}

/* ===== Reduced Motion Support ===== */
@media (prefers-reduced-motion: reduce) {
  .holographic-advanced,
  .holo-text,
  .holo-loader,
  .holo-particle {
    animation: none;
  }
  
  .holo-card:hover {
    transform: translateY(-2px);
  }
}
