/**
 * Project Modal Functionality
 * Handles the project details modal
 */

// Initialize the project modal functionality
const initProjectModal = () => {
    const modal = document.getElementById('project-modal');
    if (!modal) return;

    const modalClose = modal.querySelector('.modal-close');
    const detailButtons = document.querySelectorAll('.project-details-btn');

    // Modal elements
    const modalImage = document.getElementById('modal-project-image');
    const modalTitle = document.getElementById('modal-project-title');
    const modalCategory = document.getElementById('modal-project-category');
    const modalDescription = document.getElementById('modal-project-description');
    const modalClient = document.getElementById('modal-project-client');
    const modalDate = document.getElementById('modal-project-date');
    const modalTechnologies = document.getElementById('modal-project-technologies');
    const modalLink = document.getElementById('modal-project-link');

    // Open modal when clicking on detail buttons
    detailButtons.forEach(button => {
        button.addEventListener('click', (e) => {
            e.preventDefault();

            // Get the project card
            const projectCard = button.closest('.project-card');
            if (!projectCard) return;

            // Get project title from the card
            const projectTitle = projectCard.querySelector('.project-title').textContent;

            // Find the project data
            const project = projectsData.find(p => p.title === projectTitle);
            if (!project) return;

            // Fill the modal with project data
            modalImage.src = project.image;
            modalImage.alt = project.title;
            modalTitle.textContent = project.title;
            modalCategory.textContent = project.category;
            modalDescription.textContent = project.description;
            modalClient.textContent = project.client;
            modalDate.textContent = project.date;

            // Clear and fill technologies
            modalTechnologies.innerHTML = '';
            project.technologies.forEach(tech => {
                const techSpan = document.createElement('span');
                techSpan.className = 'modal-tech';
                techSpan.textContent = tech;
                modalTechnologies.appendChild(techSpan);
            });

            // Set the project link
            modalLink.href = project.url;

            // Make modal image and title clickable
            modalImage.style.cursor = 'pointer';
            modalTitle.style.cursor = 'pointer';

            modalImage.addEventListener('click', () => {
                window.open(project.url, '_blank');
            });

            modalTitle.addEventListener('click', () => {
                window.open(project.url, '_blank');
            });

            // Show the modal
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        });
    });

    // Close modal when clicking on close button
    if (modalClose) {
        modalClose.addEventListener('click', () => {
            modal.classList.remove('active');
            document.body.style.overflow = '';
        });
    }

    // Close modal when clicking outside the content
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.classList.remove('active');
            document.body.style.overflow = '';
        }
    });
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initProjectModal);
