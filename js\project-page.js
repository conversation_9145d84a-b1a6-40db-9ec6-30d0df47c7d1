/**
 * Project Page JavaScript
 * Handles loading project data into the project page
 */

// Load project data based on URL parameter
const loadProjectData = () => {
    // Get project ID from URL
    const urlParams = new URLSearchParams(window.location.search);
    const projectId = urlParams.get('id');

    if (!projectId) {
        console.error('No project ID specified');
        window.location.href = '../index.html#myWorks';
        return;
    }

    // Find project data
    const project = projectsData.find(p => p.id === projectId);

    if (!project) {
        console.error('Project not found');
        window.location.href = '../index.html#myWorks';
        return;
    }

    // Update page title
    document.title = `${project.title} - <PERSON><PERSON><PERSON>`;

    // Update project hero section
    document.getElementById('project-title').textContent = project.title;
    document.getElementById('project-category').textContent = project.category;
    document.getElementById('project-link').href = project.url;
    document.getElementById('project-image').src = `../${project.image}`;
    document.getElementById('project-image').alt = project.title;

    // Update project details
    document.getElementById('project-description').textContent = project.description;

    // Update project features
    const featuresList = document.getElementById('project-features');
    featuresList.innerHTML = '';

    project.features.forEach(feature => {
        const li = document.createElement('li');
        li.textContent = feature;
        featuresList.appendChild(li);
    });

    // Update project info
    document.getElementById('project-client').textContent = project.client;
    document.getElementById('project-date').textContent = project.date;

    // Update technologies
    const technologiesContainer = document.getElementById('project-technologies');
    technologiesContainer.innerHTML = '';

    project.technologies.forEach(tech => {
        const techSpan = document.createElement('span');
        techSpan.className = 'project-tech';
        techSpan.textContent = tech;
        technologiesContainer.appendChild(techSpan);
    });

    // Update gallery
    const galleryContainer = document.getElementById('project-gallery');
    galleryContainer.innerHTML = '';

    if (project.gallery && project.gallery.length > 0) {
        project.gallery.forEach(image => {
            const galleryItem = document.createElement('div');
            galleryItem.className = 'gallery-item';

            const img = document.createElement('img');
            img.src = `../${image}`;
            img.alt = project.title;

            galleryItem.appendChild(img);
            galleryContainer.appendChild(galleryItem);
        });
    } else {
        // If no gallery images, hide the section
        document.querySelector('.project-gallery-section').style.display = 'none';
    }

    // Load related projects (3 random projects excluding current one)
    loadRelatedProjects(project.id);
};

// Load related projects
const loadRelatedProjects = (currentProjectId) => {
    const relatedProjectsContainer = document.getElementById('related-projects');
    relatedProjectsContainer.innerHTML = '';

    // Filter out current project and get up to 3 random projects
    const otherProjects = projectsData.filter(p => p.id !== currentProjectId);
    const randomProjects = getRandomItems(otherProjects, 3);

    if (randomProjects.length === 0) {
        // If no related projects, hide the section
        document.querySelector('.related-projects-section').style.display = 'none';
        return;
    }

    randomProjects.forEach(project => {
        const projectCard = document.createElement('div');
        projectCard.className = 'related-project-card';

        projectCard.innerHTML = `
            <div class="related-project-image">
                <img src="../${project.image}" alt="${project.title}">
            </div>
            <div class="related-project-content">
                <h3 class="related-project-title">${project.title}</h3>
                <p class="related-project-category">${project.category}</p>
                <a href="project.html?id=${project.id}" class="btn btn-sm btn-primary">View Details</a>
            </div>
        `;

        relatedProjectsContainer.appendChild(projectCard);
    });
};

// Helper function to get random items from an array
const getRandomItems = (array, count) => {
    const shuffled = [...array].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
};

// Initialize gallery modal functionality
const initGalleryModal = () => {
    const galleryItems = document.querySelectorAll('.gallery-item');
    const modal = document.getElementById('gallery-modal');

    if (!galleryItems.length || !modal) return;

    const modalImage = modal.querySelector('.modal-image');
    const modalClose = modal.querySelector('.modal-close');

    galleryItems.forEach(item => {
        item.addEventListener('click', () => {
            const imgSrc = item.querySelector('img').getAttribute('src');
            modalImage.setAttribute('src', imgSrc);
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        });
    });

    if (modalClose) {
        modalClose.addEventListener('click', () => {
            modal.classList.remove('active');
            document.body.style.overflow = '';
        });
    }

    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.classList.remove('active');
            document.body.style.overflow = '';
        }
    });
};

// Make project image and title clickable to go to project URL
const makeProjectClickable = () => {
    const projectImage = document.getElementById('project-image');
    const projectTitle = document.getElementById('project-title');
    const projectUrl = document.getElementById('project-link').getAttribute('href');

    if (projectImage && projectUrl) {
        projectImage.style.cursor = 'pointer';
        projectImage.addEventListener('click', () => {
            window.open(projectUrl, '_blank');
        });
    }

    if (projectTitle && projectUrl) {
        projectTitle.style.cursor = 'pointer';
        projectTitle.addEventListener('click', () => {
            window.open(projectUrl, '_blank');
        });
    }
};

// Make related project cards clickable to go to project URL
const makeRelatedProjectsClickable = () => {
    const relatedProjects = document.querySelectorAll('.related-project-card');

    relatedProjects.forEach(card => {
        const projectId = card.querySelector('a').getAttribute('href').split('=')[1];
        const project = projectsData.find(p => p.id === projectId);

        if (project) {
            const projectImage = card.querySelector('.related-project-image');
            const projectTitle = card.querySelector('.related-project-title');

            if (projectImage) {
                projectImage.style.cursor = 'pointer';
                projectImage.addEventListener('click', () => {
                    window.open(project.url, '_blank');
                });
            }

            if (projectTitle) {
                projectTitle.style.cursor = 'pointer';
                projectTitle.addEventListener('click', () => {
                    window.open(project.url, '_blank');
                });
            }
        }
    });
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    loadProjectData();
    initGalleryModal();

    // Add a small delay to ensure all elements are loaded
    setTimeout(() => {
        makeProjectClickable();
        makeRelatedProjectsClickable();
    }, 500);
});
