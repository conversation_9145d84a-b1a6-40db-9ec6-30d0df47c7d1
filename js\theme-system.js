/**
 * Advanced Theme System 2025
 * Smart theme switching with auto-detection and smooth transitions
 */

class ThemeSystem {
  constructor(options = {}) {
    this.options = {
      storageKey: 'portfolio-theme-2025',
      autoDetect: true,
      smoothTransition: true,
      animationDuration: 300,
      ...options
    };
    
    this.currentTheme = null;
    this.systemTheme = null;
    this.toggleButton = null;
    
    this.init();
  }

  init() {
    this.detectSystemTheme();
    this.loadSavedTheme();
    this.createToggleButton();
    this.bindEvents();
    this.applyTheme(this.currentTheme);
    
    console.log('🎨 Advanced Theme System 2025 initialized');
  }

  detectSystemTheme() {
    if (window.matchMedia) {
      this.systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      
      // Listen for system theme changes
      window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        this.systemTheme = e.matches ? 'dark' : 'light';
        
        // If user hasn't set a preference, follow system
        if (!localStorage.getItem(this.options.storageKey)) {
          this.setTheme(this.systemTheme);
        }
      });
    } else {
      this.systemTheme = 'dark'; // Default fallback
    }
  }

  loadSavedTheme() {
    const savedTheme = localStorage.getItem(this.options.storageKey);
    
    if (savedTheme && ['dark', 'light'].includes(savedTheme)) {
      this.currentTheme = savedTheme;
    } else if (this.options.autoDetect) {
      this.currentTheme = this.systemTheme;
    } else {
      this.currentTheme = 'dark'; // Default
    }
  }

  createToggleButton() {
    // Find existing toggle or create new one
    this.toggleButton = document.querySelector('.theme-toggle-advanced');
    
    if (!this.toggleButton) {
      this.toggleButton = document.createElement('button');
      this.toggleButton.className = 'theme-toggle-advanced';
      this.toggleButton.setAttribute('aria-label', 'Toggle theme');
      this.toggleButton.setAttribute('title', 'Switch between dark and light themes');
      
      // Add icons
      this.toggleButton.innerHTML = `
        <span class="icon icon-moon">🌙</span>
        <span class="icon icon-sun">☀️</span>
      `;
      
      // Add to navigation or header
      const nav = document.querySelector('nav') || document.querySelector('header');
      if (nav) {
        nav.appendChild(this.toggleButton);
      } else {
        // Create floating toggle
        this.toggleButton.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          z-index: 9999;
        `;
        document.body.appendChild(this.toggleButton);
      }
    }
  }

  bindEvents() {
    if (this.toggleButton) {
      this.toggleButton.addEventListener('click', () => {
        this.toggleTheme();
      });
    }

    // Keyboard shortcut (Ctrl/Cmd + Shift + T)
    document.addEventListener('keydown', (e) => {
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'T') {
        e.preventDefault();
        this.toggleTheme();
      }
    });

    // Listen for theme change events from other parts of the app
    document.addEventListener('themeChange', (e) => {
      this.setTheme(e.detail.theme);
    });
  }

  setTheme(theme) {
    if (!['dark', 'light'].includes(theme)) {
      console.warn(`Invalid theme: ${theme}`);
      return;
    }

    const oldTheme = this.currentTheme;
    this.currentTheme = theme;

    // Save to localStorage
    localStorage.setItem(this.options.storageKey, theme);

    // Apply theme
    this.applyTheme(theme);

    // Update toggle button
    this.updateToggleButton();

    // Trigger transition animation
    if (this.options.smoothTransition && oldTheme !== theme) {
      this.playTransitionAnimation();
    }

    // Dispatch custom event
    document.dispatchEvent(new CustomEvent('themeChanged', {
      detail: { 
        oldTheme, 
        newTheme: theme,
        isSystemTheme: theme === this.systemTheme
      }
    }));

    console.log(`🎨 Theme changed to: ${theme}`);
  }

  applyTheme(theme) {
    // Remove existing theme classes
    document.documentElement.removeAttribute('data-theme');
    document.body.classList.remove('theme-dark', 'theme-light');

    // Apply new theme
    document.documentElement.setAttribute('data-theme', theme);
    document.body.classList.add(`theme-${theme}`);

    // Update meta theme-color for mobile browsers
    this.updateMetaThemeColor(theme);

    // Update CSS custom properties for immediate effect
    this.updateCSSProperties(theme);
  }

  updateMetaThemeColor(theme) {
    let metaThemeColor = document.querySelector('meta[name="theme-color"]');
    
    if (!metaThemeColor) {
      metaThemeColor = document.createElement('meta');
      metaThemeColor.name = 'theme-color';
      document.head.appendChild(metaThemeColor);
    }

    const colors = {
      dark: '#0a0a12',
      light: '#ffffff'
    };

    metaThemeColor.content = colors[theme];
  }

  updateCSSProperties(theme) {
    const root = document.documentElement;
    
    // Force immediate update of critical properties
    if (theme === 'dark') {
      root.style.setProperty('--bg-primary', '#0a0a12');
      root.style.setProperty('--text-primary', '#ffffff');
    } else {
      root.style.setProperty('--bg-primary', '#ffffff');
      root.style.setProperty('--text-primary', '#1a202c');
    }
  }

  updateToggleButton() {
    if (!this.toggleButton) return;

    this.toggleButton.classList.toggle('light', this.currentTheme === 'light');
    
    const moonIcon = this.toggleButton.querySelector('.icon-moon');
    const sunIcon = this.toggleButton.querySelector('.icon-sun');
    
    if (this.currentTheme === 'light') {
      this.toggleButton.setAttribute('aria-label', 'Switch to dark theme');
      this.toggleButton.setAttribute('title', 'Switch to dark theme');
    } else {
      this.toggleButton.setAttribute('aria-label', 'Switch to light theme');
      this.toggleButton.setAttribute('title', 'Switch to light theme');
    }
  }

  toggleTheme() {
    const newTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
    this.setTheme(newTheme);
  }

  playTransitionAnimation() {
    if (!this.options.smoothTransition) return;

    // Add transition class to body
    document.body.classList.add('theme-switching');

    // Remove after animation
    setTimeout(() => {
      document.body.classList.remove('theme-switching');
    }, this.options.animationDuration);

    // Create ripple effect from toggle button
    if (this.toggleButton) {
      this.createRippleEffect();
    }
  }

  createRippleEffect() {
    const rect = this.toggleButton.getBoundingClientRect();
    const ripple = document.createElement('div');
    
    ripple.style.cssText = `
      position: fixed;
      top: ${rect.top + rect.height / 2}px;
      left: ${rect.left + rect.width / 2}px;
      width: 0;
      height: 0;
      border-radius: 50%;
      background: var(--accent-primary);
      opacity: 0.1;
      pointer-events: none;
      z-index: 9998;
      transform: translate(-50%, -50%);
      transition: all ${this.options.animationDuration}ms ease-out;
    `;

    document.body.appendChild(ripple);

    // Trigger animation
    requestAnimationFrame(() => {
      const size = Math.max(window.innerWidth, window.innerHeight) * 2;
      ripple.style.width = `${size}px`;
      ripple.style.height = `${size}px`;
      ripple.style.opacity = '0';
    });

    // Clean up
    setTimeout(() => {
      if (ripple.parentNode) {
        ripple.parentNode.removeChild(ripple);
      }
    }, this.options.animationDuration);
  }

  // Public API methods
  getCurrentTheme() {
    return this.currentTheme;
  }

  getSystemTheme() {
    return this.systemTheme;
  }

  isUsingSystemTheme() {
    return this.currentTheme === this.systemTheme;
  }

  resetToSystemTheme() {
    localStorage.removeItem(this.options.storageKey);
    this.setTheme(this.systemTheme);
  }

  // Theme presets
  setThemePreset(preset) {
    const presets = {
      'auto': this.systemTheme,
      'dark': 'dark',
      'light': 'light',
      'midnight': 'dark', // Could be extended with custom themes
      'sunrise': 'light'
    };

    if (presets[preset]) {
      this.setTheme(presets[preset]);
    }
  }
}

// Auto-initialize theme system
document.addEventListener('DOMContentLoaded', () => {
  window.themeSystem = new ThemeSystem({
    autoDetect: true,
    smoothTransition: true,
    animationDuration: 300
  });
});

// Export for external use
window.ThemeSystem = ThemeSystem;
