<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Details - <PERSON><PERSON><PERSON></title>
    <meta name="description" content="Detailed information about the project">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="../assets/svg/logo.svg">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../style.css">
    <link rel="stylesheet" href="../css/variables.css">
    <link rel="stylesheet" href="../css/animations.css">
    <link rel="stylesheet" href="../css/utilities.css">
    <link rel="stylesheet" href="../css/backgrounds.css">
    <link rel="stylesheet" href="../css/components.css">
    <link rel="stylesheet" href="../css/modern-2025.css">
    <link rel="stylesheet" href="../css/tabs-timeline.css">
    <link rel="stylesheet" href="../css/project-page.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Modern Libraries -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/locomotive-scroll@4.1.4/dist/locomotive-scroll.min.css">
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tippy.js@6/animations/scale.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tippy.js@6/themes/light.css" />
</head>
<body>
    <!-- Particles Background Canvas -->
    <canvas id="particles-canvas" class="particles-js"></canvas>

    <!-- Mobile Menu Overlay -->
    <div class="mobile-menu-overlay"></div>

    <!-- Mobile Menu -->
    <div id="mobile-menu" class="mobile-menu">
        <ul class="mobile-nav-list">
            <li><a href="../index.html#home" class="mobile-nav-link">Home</a></li>
            <li><a href="../index.html#aboutMe" class="mobile-nav-link">About</a></li>
            <li><a href="../index.html#skills" class="mobile-nav-link">Skills</a></li>
            <li><a href="../index.html#services" class="mobile-nav-link">Services</a></li>
            <li><a href="../index.html#myWorks" class="mobile-nav-link">Projects</a></li>
            <li><a href="../index.html#testimonials" class="mobile-nav-link">Testimonials</a></li>
            <li><a href="../index.html#contactMe" class="mobile-nav-link">Contact</a></li>
        </ul>
    </div>

    <!-- Header -->
    <header class="main-header">
        <div class="container nav-container">
            <a href="../index.html" class="logo">
                <img src="../assets/svg/logo.svg" alt="Youssef Ahmed Logo">
                <span>Youssef Ahmed</span>
            </a>

            <nav class="main-nav">
                <ul class="nav-list">
                    <li><a href="../index.html#home" class="nav-link">Home</a></li>
                    <li><a href="../index.html#aboutMe" class="nav-link">About</a></li>
                    <li><a href="../index.html#skills" class="nav-link">Skills</a></li>
                    <li><a href="../index.html#services" class="nav-link">Services</a></li>
                    <li><a href="../index.html#myWorks" class="nav-link">Projects</a></li>
                    <li><a href="../index.html#testimonials" class="nav-link">Testimonials</a></li>
                    <li><a href="../index.html#contactMe" class="nav-link">Contact</a></li>
                </ul>
            </nav>

            <div class="nav-actions">
                <button id="theme-toggle" class="theme-toggle" aria-label="Toggle Theme">
                    <i class="fas fa-sun"></i>
                    <i class="fas fa-moon"></i>
                </button>

                <button id="menu-toggle" class="menu-toggle" aria-label="Toggle Menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </header>

    <!-- Project Hero Section -->
    <section class="project-hero-section">
        <div class="container">
            <div class="project-hero-content">
                <h1 id="project-title" class="project-hero-title gradient-text">Project Title</h1>
                <p id="project-category" class="project-hero-category">Project Category</p>
                <div class="project-hero-actions">
                    <a id="project-link" href="#" target="_blank" class="btn btn-primary">View Live Project</a>
                    <a href="../index.html#myWorks" class="btn btn-secondary">Back to Projects</a>
                </div>
            </div>
            <div class="project-hero-image">
                <img id="project-image" src="../assets/svg/Youssef Ahmed.png" alt="Project Image">
            </div>
        </div>
    </section>

    <!-- Project Details Section -->
    <section class="project-details-section">
        <div class="container">
            <div class="project-details-grid">
                <div class="project-details-content">
                    <h2 class="section-title">Project Overview</h2>
                    <p id="project-description" class="project-description">Project description goes here.</p>
                    
                    <h3 class="features-title">Key Features</h3>
                    <ul id="project-features" class="features-list">
                        <!-- Features will be added dynamically -->
                    </ul>
                </div>
                <div class="project-details-sidebar">
                    <div class="project-info-card glass-effect">
                        <h3>Project Information</h3>
                        <div class="project-info-item">
                            <h4>Client</h4>
                            <p id="project-client">Client Name</p>
                        </div>
                        <div class="project-info-item">
                            <h4>Date</h4>
                            <p id="project-date">Project Date</p>
                        </div>
                        <div class="project-info-item">
                            <h4>Technologies</h4>
                            <div id="project-technologies" class="project-technologies">
                                <!-- Technologies will be added dynamically -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Project Gallery Section -->
    <section class="project-gallery-section">
        <div class="container">
            <h2 class="section-title">Project Gallery</h2>
            <div id="project-gallery" class="project-gallery-grid">
                <!-- Gallery images will be added dynamically -->
            </div>
        </div>
    </section>

    <!-- Related Projects Section -->
    <section class="related-projects-section">
        <div class="container">
            <h2 class="section-title">Related Projects</h2>
            <div id="related-projects" class="related-projects-grid">
                <!-- Related projects will be added dynamically -->
            </div>
        </div>
    </section>

    <!-- Gallery Modal -->
    <div id="gallery-modal" class="gallery-modal">
        <span class="modal-close">&times;</span>
        <img class="modal-image" src="">
    </div>

    <!-- Footer -->
    <footer class="main-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <img src="../assets/svg/logo.svg" alt="Youssef Ahmed Logo">
                    <h3>Youssef Ahmed</h3>
                </div>
                <div class="footer-links">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="../index.html#home">Home</a></li>
                        <li><a href="../index.html#aboutMe">About</a></li>
                        <li><a href="../index.html#skills">Skills</a></li>
                        <li><a href="../index.html#services">Services</a></li>
                        <li><a href="../index.html#myWorks">Projects</a></li>
                        <li><a href="../index.html#contactMe">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-social">
                    <h4>Connect With Me</h4>
                    <div class="social-icons">
                        <a href="#" class="social-icon" aria-label="LinkedIn">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        <a href="#" class="social-icon" aria-label="GitHub">
                            <i class="fab fa-github"></i>
                        </a>
                        <a href="#" class="social-icon" aria-label="Twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-icon" aria-label="Instagram">
                            <i class="fab fa-instagram"></i>
                        </a>
                    </div>
                </div>
                <div class="footer-contact">
                    <h4>Contact Info</h4>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +****************</p>
                    <p><i class="fas fa-map-marker-alt"></i> Cairo, Egypt</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; <span id="current-year">2023</span> Youssef Ahmed. All Rights Reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <a href="#" class="back-to-top" aria-label="Back to Top">
        <i class="fas fa-arrow-up"></i>
    </a>

    <!-- JavaScript Libraries -->
    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/locomotive-scroll@4.1.4/dist/locomotive-scroll.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.5/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.5/ScrollTrigger.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/build/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanta@latest/dist/vanta.net.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/tippy.js@6/dist/tippy-bundle.umd.js"></script>

    <!-- Custom JavaScript Files -->
    <script src="../js/projects-data.js"></script>
    <script src="../js/animations.js"></script>
    <script src="../js/main.js"></script>
    <script src="../js/modern-effects.js"></script>
    <script src="../js/tabs.js"></script>
    <script src="../js/force-visibility.js"></script>
    <script src="../js/project-page.js"></script>
</body>
</html>
