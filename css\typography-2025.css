/*
 * Typography 2025 - Variable Fonts & Advanced Text Effects
 */

/* ===== Variable Fonts Import ===== */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100..800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@100..900&display=swap');

/* ===== Typography Base ===== */
.heading-2025 {
  font-family: 'Inter', sans-serif;
  font-variation-settings: 
    'wght' 700,
    'slnt' 0;
  font-size: clamp(2rem, 5vw, 4rem);
  line-height: 1.1;
  letter-spacing: -0.02em;
  
  /* Gradient text with animation */
  background: var(--neural-gradient);
  background-size: 200% auto;
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  animation: gradientText 3s linear infinite;
  
  /* Text shadow for depth */
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

@keyframes gradientText {
  0% { background-position: 0% center; }
  50% { background-position: 100% center; }
  100% { background-position: 200% center; }
}

.subheading-2025 {
  font-family: 'Inter', sans-serif;
  font-variation-settings: 'wght' 500;
  font-size: clamp(1.2rem, 3vw, 2rem);
  line-height: 1.3;
  letter-spacing: -0.01em;
  color: var(--text-secondary);
  position: relative;
}

.body-text-2025 {
  font-family: 'Inter', sans-serif;
  font-variation-settings: 'wght' 400;
  font-size: clamp(1rem, 2vw, 1.125rem);
  line-height: 1.6;
  color: var(--text-primary);
  font-feature-settings: 'liga' 1, 'calt' 1;
}

.code-text-2025 {
  font-family: 'JetBrains Mono', monospace;
  font-variation-settings: 'wght' 400;
  font-size: 0.9rem;
  background: rgba(18, 18, 30, 0.8);
  padding: 0.2em 0.4em;
  border-radius: 4px;
  border: 1px solid rgba(157, 78, 221, 0.3);
}

/* ===== Arabic Typography ===== */
.arabic-text {
  font-family: 'Noto Sans Arabic', sans-serif;
  direction: rtl;
  text-align: right;
  font-feature-settings: 'liga' 1, 'calt' 1;
  line-height: 1.8;
}

.arabic-heading {
  font-family: 'Noto Sans Arabic', sans-serif;
  font-weight: 700;
  direction: rtl;
  text-align: right;
  background: var(--holographic-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* ===== Advanced Text Effects ===== */
.neon-text-2025 {
  color: var(--cyber-green);
  text-shadow: 
    0 0 5px currentColor,
    0 0 10px currentColor,
    0 0 15px currentColor,
    0 0 20px currentColor;
  animation: neonFlicker 2s ease-in-out infinite alternate;
}

@keyframes neonFlicker {
  0%, 100% {
    text-shadow: 
      0 0 5px currentColor,
      0 0 10px currentColor,
      0 0 15px currentColor,
      0 0 20px currentColor;
  }
  50% {
    text-shadow: 
      0 0 2px currentColor,
      0 0 5px currentColor,
      0 0 8px currentColor,
      0 0 12px currentColor;
  }
}

.glitch-text {
  position: relative;
  color: var(--text-primary);
  font-weight: 700;
}

.glitch-text::before,
.glitch-text::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch-text::before {
  animation: glitch-1 0.5s infinite;
  color: var(--holographic-pink);
  z-index: -1;
}

.glitch-text::after {
  animation: glitch-2 0.5s infinite;
  color: var(--cyber-green);
  z-index: -2;
}

@keyframes glitch-1 {
  0%, 14%, 15%, 49%, 50%, 99%, 100% {
    transform: translate(0);
  }
  15%, 49% {
    transform: translate(-2px, 2px);
  }
}

@keyframes glitch-2 {
  0%, 20%, 21%, 62%, 63%, 99%, 100% {
    transform: translate(0);
  }
  21%, 62% {
    transform: translate(2px, -2px);
  }
}

.typewriter-2025 {
  overflow: hidden;
  border-right: 2px solid var(--accent-primary);
  white-space: nowrap;
  animation: 
    typing 3.5s steps(40, end),
    blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: var(--accent-primary); }
}

.text-reveal {
  position: relative;
  overflow: hidden;
}

.text-reveal::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-primary);
  animation: textReveal 1.5s ease-out forwards;
}

@keyframes textReveal {
  0% { transform: translateX(0); }
  100% { transform: translateX(100%); }
}

/* ===== Responsive Typography ===== */
@media (max-width: 768px) {
  .heading-2025 {
    font-size: clamp(1.5rem, 8vw, 2.5rem);
    line-height: 1.2;
  }
  
  .subheading-2025 {
    font-size: clamp(1rem, 5vw, 1.5rem);
  }
  
  .body-text-2025 {
    font-size: clamp(0.9rem, 4vw, 1rem);
    line-height: 1.7;
  }
}

/* ===== Text Selection ===== */
::selection {
  background: var(--holographic-gradient);
  color: var(--bg-primary);
}

::-moz-selection {
  background: var(--holographic-gradient);
  color: var(--bg-primary);
}

/* ===== Focus Styles ===== */
.text-input-2025:focus {
  outline: none;
  box-shadow: 
    0 0 0 2px var(--accent-primary),
    0 0 20px rgba(157, 78, 221, 0.3);
}

/* ===== Text Utilities ===== */
.text-gradient {
  background: var(--holographic-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.text-shadow-soft {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.text-shadow-hard {
  text-shadow: 2px 2px 0px rgba(0, 0, 0, 0.8);
}

.text-outline {
  -webkit-text-stroke: 1px var(--accent-primary);
  color: transparent;
}
