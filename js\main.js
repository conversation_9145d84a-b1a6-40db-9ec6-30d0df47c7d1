// الوظائف الرئيسية للموقع

// تهيئة الموقع
const initSite = () => {
  // تحديد العام الحالي في التذييل
  const yearElement = document.getElementById('current-year');
  if (yearElement) {
    yearElement.textContent = new Date().getFullYear();
  }
  
  // تفعيل التنقل النشط
  updateActiveNavLinks();
  
  // تهيئة نظام الفلترة للمشاريع
  initProjectFilters();
  
  // تهيئة نظام الاتصال
  initContactForm();
  
  // تهيئة عداد الإحصائيات
  initCounters();
  
  // تهيئة الشرائح
  initSliders();
};

// تحديث روابط التنقل النشطة
const updateActiveNavLinks = () => {
  const sections = document.querySelectorAll('section[id]');
  const navLinks = document.querySelectorAll('.nav-link');
  
  window.addEventListener('scroll', () => {
    let current = '';
    const scrollPosition = window.scrollY + 100; // تعويض للهامش العلوي
    
    sections.forEach(section => {
      const sectionTop = section.offsetTop;
      const sectionHeight = section.offsetHeight;
      
      if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
        current = section.getAttribute('id');
      }
    });
    
    navLinks.forEach(link => {
      link.classList.remove('active');
      if (link.getAttribute('href') === `#${current}`) {
        link.classList.add('active');
      }
    });
  });
};

// نظام فلترة المشاريع
const initProjectFilters = () => {
  const filterButtons = document.querySelectorAll('.filter-btn');
  const projectItems = document.querySelectorAll('.project-item');
  
  if (!filterButtons.length || !projectItems.length) return;
  
  filterButtons.forEach(button => {
    button.addEventListener('click', () => {
      // إزالة الفئة النشطة من جميع الأزرار
      filterButtons.forEach(btn => btn.classList.remove('active'));
      
      // إضافة الفئة النشطة للزر المحدد
      button.classList.add('active');
      
      // الحصول على فئة الفلتر
      const filterValue = button.getAttribute('data-filter');
      
      // فلترة المشاريع
      projectItems.forEach(item => {
        if (filterValue === 'all') {
          item.style.display = 'block';
        } else {
          if (item.classList.contains(filterValue)) {
            item.style.display = 'block';
          } else {
            item.style.display = 'none';
          }
        }
        
        // إضافة تأثير الظهور بعد الفلترة
        setTimeout(() => {
          if (item.style.display === 'block') {
            item.classList.add('show');
          } else {
            item.classList.remove('show');
          }
        }, 100);
      });
    });
  });
  
  // تفعيل الفلتر الأول افتراضيًا
  if (filterButtons.length > 0) {
    filterButtons[0].click();
  }
};

// نظام نموذج الاتصال
const initContactForm = () => {
  const contactForm = document.getElementById('contact-form');
  if (!contactForm) return;
  
  contactForm.addEventListener('submit', (e) => {
    e.preventDefault();
    
    // الحصول على قيم الحقول
    const name = contactForm.querySelector('#name').value;
    const email = contactForm.querySelector('#email').value;
    const message = contactForm.querySelector('#message').value;
    
    // التحقق من صحة البيانات
    if (!name || !email || !message) {
      showFormMessage('error', 'يرجى ملء جميع الحقول المطلوبة');
      return;
    }
    
    // محاكاة إرسال النموذج (في الإنتاج، سيتم استبدال هذا بإرسال فعلي للبيانات)
    showFormMessage('loading', 'جاري إرسال الرسالة...');
    
    setTimeout(() => {
      // محاكاة استجابة ناجحة
      showFormMessage('success', 'تم إرسال رسالتك بنجاح!');
      contactForm.reset();
    }, 1500);
  });
  
  // عرض رسائل النموذج
  const showFormMessage = (type, message) => {
    const messageElement = contactForm.querySelector('.form-message');
    if (!messageElement) return;
    
    messageElement.textContent = message;
    messageElement.className = 'form-message';
    messageElement.classList.add(type);
    
    if (type === 'success' || type === 'error') {
      setTimeout(() => {
        messageElement.classList.add('fade-out');
        setTimeout(() => {
          messageElement.textContent = '';
          messageElement.className = 'form-message';
        }, 500);
      }, 3000);
    }
  };
};

// عدادات الإحصائيات
const initCounters = () => {
  const counters = document.querySelectorAll('.counter');
  if (!counters.length) return;
  
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const counter = entry.target;
        const target = parseInt(counter.getAttribute('data-target'));
        const duration = parseInt(counter.getAttribute('data-duration') || '2000');
        let current = 0;
        
        const increment = target / (duration / 16); // 16ms هي تقريبًا 60fps
        
        const updateCounter = () => {
          current += increment;
          if (current < target) {
            counter.textContent = Math.floor(current);
            requestAnimationFrame(updateCounter);
          } else {
            counter.textContent = target;
          }
        };
        
        updateCounter();
        observer.unobserve(counter);
      }
    });
  }, {
    threshold: 0.5
  });
  
  counters.forEach(counter => {
    observer.observe(counter);
  });
};

// تهيئة الشرائح
const initSliders = () => {
  const sliders = document.querySelectorAll('.slider');
  if (!sliders.length) return;
  
  sliders.forEach(slider => {
    const slidesContainer = slider.querySelector('.slides');
    const slides = slider.querySelectorAll('.slide');
    const prevBtn = slider.querySelector('.prev-slide');
    const nextBtn = slider.querySelector('.next-slide');
    const dotsContainer = slider.querySelector('.slider-dots');
    
    if (!slidesContainer || !slides.length) return;
    
    let currentIndex = 0;
    const slideWidth = slides[0].offsetWidth;
    const slideMargin = parseInt(window.getComputedStyle(slides[0]).marginRight);
    const totalWidth = slideWidth + slideMargin;
    
    // إنشاء نقاط التنقل
    if (dotsContainer) {
      slides.forEach((_, index) => {
        const dot = document.createElement('span');
        dot.classList.add('slider-dot');
        if (index === 0) dot.classList.add('active');
        
        dot.addEventListener('click', () => {
          goToSlide(index);
        });
        
        dotsContainer.appendChild(dot);
      });
    }
    
    // وظيفة الانتقال إلى شريحة محددة
    const goToSlide = (index) => {
      currentIndex = index;
      slidesContainer.style.transform = `translateX(-${currentIndex * totalWidth}px)`;
      
      // تحديث النقاط النشطة
      if (dotsContainer) {
        const dots = dotsContainer.querySelectorAll('.slider-dot');
        dots.forEach((dot, i) => {
          dot.classList.toggle('active', i === currentIndex);
        });
      }
    };
    
    // أزرار التنقل
    if (prevBtn) {
      prevBtn.addEventListener('click', () => {
        currentIndex = (currentIndex > 0) ? currentIndex - 1 : slides.length - 1;
        goToSlide(currentIndex);
      });
    }
    
    if (nextBtn) {
      nextBtn.addEventListener('click', () => {
        currentIndex = (currentIndex < slides.length - 1) ? currentIndex + 1 : 0;
        goToSlide(currentIndex);
      });
    }
    
    // التمرير التلقائي (إذا كان مطلوبًا)
    if (slider.hasAttribute('data-auto-slide')) {
      const interval = parseInt(slider.getAttribute('data-auto-slide')) || 5000;
      
      setInterval(() => {
        currentIndex = (currentIndex < slides.length - 1) ? currentIndex + 1 : 0;
        goToSlide(currentIndex);
      }, interval);
    }
    
    // دعم اللمس للأجهزة المحمولة
    let touchStartX = 0;
    let touchEndX = 0;
    
    slidesContainer.addEventListener('touchstart', (e) => {
      touchStartX = e.changedTouches[0].screenX;
    });
    
    slidesContainer.addEventListener('touchend', (e) => {
      touchEndX = e.changedTouches[0].screenX;
      handleSwipe();
    });
    
    const handleSwipe = () => {
      const swipeThreshold = 50;
      
      if (touchEndX < touchStartX - swipeThreshold) {
        // سحب لليسار (التالي)
        currentIndex = (currentIndex < slides.length - 1) ? currentIndex + 1 : 0;
      } else if (touchEndX > touchStartX + swipeThreshold) {
        // سحب لليمين (السابق)
        currentIndex = (currentIndex > 0) ? currentIndex - 1 : slides.length - 1;
      }
      
      goToSlide(currentIndex);
    };
  });
};

// عند الضغط على صورة المشروع يتم الانتقال للرابط الحي
const enableProjectImageClick = () => {
  document.querySelectorAll('.project-image').forEach(imageDiv => {
    const liveLink = imageDiv.querySelector('.project-link');
    if (liveLink && liveLink.getAttribute('href') && liveLink.getAttribute('href') !== '#') {
      imageDiv.addEventListener('click', (e) => {
        // منع التداخل مع أزرار التفاصيل
        if (e.target.closest('.project-details-btn')) return;
        window.open(liveLink.getAttribute('href'), '_blank');
      });
    }
  });
};

document.addEventListener('DOMContentLoaded', initSite);
document.addEventListener('DOMContentLoaded', enableProjectImageClick);
