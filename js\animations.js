// تأثيرات التمرير والظهور
const animateOnScroll = () => {
  const elements = document.querySelectorAll('.animate-on-scroll');
  
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        // استخراج فئات التأثير من السمات
        const animationClass = entry.target.dataset.animation || 'fade-in-up';
        const delay = entry.target.dataset.delay || '';
        
        // إضافة فئات التأثير
        entry.target.classList.add(animationClass);
        if (delay) {
          entry.target.classList.add(delay);
        }
        
        // إزالة العنصر من المراقبة بعد تطبيق التأثير
        observer.unobserve(entry.target);
      }
    });
  }, {
    threshold: 0.1, // يظهر التأثير عندما يكون 10% من العنصر مرئيًا
    rootMargin: '0px 0px -50px 0px' // يبدأ التأثير قبل أن يصل العنصر إلى الشاشة بـ 50 بكسل
  });
  
  elements.forEach(element => {
    observer.observe(element);
  });
};

// تأثير الكتابة
const typeWriter = () => {
  const elements = document.querySelectorAll('.typewriter');
  
  elements.forEach(element => {
    const text = element.dataset.text || '';
    const speed = parseInt(element.dataset.speed) || 100;
    
    element.innerHTML = '';
    let i = 0;
    
    function type() {
      if (i < text.length) {
        element.innerHTML += text.charAt(i);
        i++;
        setTimeout(type, speed);
      } else {
        // إضافة فئة لإظهار المؤشر بعد الانتهاء من الكتابة
        element.classList.add('typewriter-done');
      }
    }
    
    type();
  });
};

// تأثير الجزيئات في الخلفية
const initParticles = () => {
  const canvas = document.getElementById('particles-canvas');
  if (!canvas) return;
  
  const ctx = canvas.getContext('2d');
  
  // ضبط حجم الكانفاس ليناسب الشاشة
  const resizeCanvas = () => {
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
  };
  
  window.addEventListener('resize', resizeCanvas);
  resizeCanvas();
  
  // إنشاء الجزيئات
  const particlesArray = [];
  const numberOfParticles = 100;
  
  class Particle {
    constructor() {
      this.x = Math.random() * canvas.width;
      this.y = Math.random() * canvas.height;
      this.size = Math.random() * 3 + 1;
      this.speedX = Math.random() * 1 - 0.5;
      this.speedY = Math.random() * 1 - 0.5;
      this.color = `rgba(6, 182, 212, ${Math.random() * 0.5 + 0.1})`;
    }
    
    update() {
      this.x += this.speedX;
      this.y += this.speedY;
      
      // إعادة تعيين الجزيئات عند خروجها من الشاشة
      if (this.x > canvas.width || this.x < 0) {
        this.speedX = -this.speedX;
      }
      
      if (this.y > canvas.height || this.y < 0) {
        this.speedY = -this.speedY;
      }
    }
    
    draw() {
      ctx.fillStyle = this.color;
      ctx.beginPath();
      ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
      ctx.fill();
    }
  }
  
  // إنشاء الجزيئات
  const init = () => {
    for (let i = 0; i < numberOfParticles; i++) {
      particlesArray.push(new Particle());
    }
  };
  
  // رسم الجزيئات وتحديثها
  const animate = () => {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    for (let i = 0; i < particlesArray.length; i++) {
      particlesArray[i].update();
      particlesArray[i].draw();
    }
    
    // رسم الخطوط بين الجزيئات القريبة
    connectParticles();
    
    requestAnimationFrame(animate);
  };
  
  // رسم خطوط بين الجزيئات القريبة
  const connectParticles = () => {
    for (let a = 0; a < particlesArray.length; a++) {
      for (let b = a; b < particlesArray.length; b++) {
        const dx = particlesArray[a].x - particlesArray[b].x;
        const dy = particlesArray[a].y - particlesArray[b].y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance < 100) {
          const opacity = 1 - distance / 100;
          ctx.strokeStyle = `rgba(6, 182, 212, ${opacity * 0.2})`;
          ctx.lineWidth = 1;
          ctx.beginPath();
          ctx.moveTo(particlesArray[a].x, particlesArray[a].y);
          ctx.lineTo(particlesArray[b].x, particlesArray[b].y);
          ctx.stroke();
        }
      }
    }
  };
  
  init();
  animate();
};

// تأثير التمويج للخلفية
const initWaveBackground = () => {
  const waveElements = document.querySelectorAll('.wave-bg');
  
  waveElements.forEach(element => {
    // إنشاء عنصر SVG للتمويج
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('class', 'wave-svg');
    svg.setAttribute('viewBox', '0 0 1440 320');
    svg.setAttribute('preserveAspectRatio', 'none');
    
    // إنشاء مسار التمويج
    const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    path.setAttribute('d', 'M0,192L48,181.3C96,171,192,149,288,154.7C384,160,480,192,576,202.7C672,213,768,203,864,181.3C960,160,1056,128,1152,117.3C1248,107,1344,117,1392,122.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z');
    path.setAttribute('fill', 'rgba(6, 182, 212, 0.1)');
    path.setAttribute('class', 'wave-path');
    
    svg.appendChild(path);
    element.appendChild(svg);
    
    // إنشاء طبقة ثانية من التمويج
    const svg2 = svg.cloneNode(true);
    const path2 = svg2.querySelector('path');
    path2.setAttribute('d', 'M0,256L48,240C96,224,192,192,288,181.3C384,171,480,181,576,186.7C672,192,768,192,864,176C960,160,1056,128,1152,117.3C1248,107,1344,117,1392,122.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z');
    path2.setAttribute('fill', 'rgba(6, 182, 212, 0.05)');
    path2.setAttribute('class', 'wave-path-2');
    
    element.appendChild(svg2);
  });
};

// تبديل وضع الألوان (داكن/فاتح)
const initThemeToggle = () => {
  const themeToggle = document.getElementById('theme-toggle');
  if (!themeToggle) return;
  
  // التحقق من وجود تفضيل مخزن
  const currentTheme = localStorage.getItem('theme') || 'dark';
  document.documentElement.setAttribute('data-theme', currentTheme);
  
  // تحديث حالة الزر
  if (currentTheme === 'light') {
    themeToggle.classList.add('active');
  }
  
  themeToggle.addEventListener('click', () => {
    // تبديل السمة
    const newTheme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'light' : 'dark';
    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
    
    // تحديث حالة الزر
    themeToggle.classList.toggle('active');
  });
};

// تأثير التمرير السلس
const initSmoothScroll = () => {
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      
      const targetId = this.getAttribute('href');
      if (targetId === '#') return;
      
      const targetElement = document.querySelector(targetId);
      if (!targetElement) return;
      
      window.scrollTo({
        top: targetElement.offsetTop - 80, // تعويض لارتفاع الهيدر
        behavior: 'smooth'
      });
    });
  });
};

// تأثير القائمة المتنقلة
const initMobileMenu = () => {
  const menuToggle = document.getElementById('menu-toggle');
  const mobileMenu = document.getElementById('mobile-menu');
  
  if (!menuToggle || !mobileMenu) return;
  
  menuToggle.addEventListener('click', () => {
    menuToggle.classList.toggle('active');
    mobileMenu.classList.toggle('active');
    document.body.classList.toggle('menu-open');
  });
  
  // إغلاق القائمة عند النقر على الروابط
  mobileMenu.querySelectorAll('a').forEach(link => {
    link.addEventListener('click', () => {
      menuToggle.classList.remove('active');
      mobileMenu.classList.remove('active');
      document.body.classList.remove('menu-open');
    });
  });
};

// تأثير تثبيت الهيدر عند التمرير
const initStickyHeader = () => {
  const header = document.querySelector('header');
  if (!header) return;
  
  const headerHeight = header.offsetHeight;
  let lastScrollTop = 0;
  
  window.addEventListener('scroll', () => {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    
    if (scrollTop > headerHeight) {
      header.classList.add('sticky');
      
      // إخفاء الهيدر عند التمرير للأسفل وإظهاره عند التمرير للأعلى
      if (scrollTop > lastScrollTop) {
        header.classList.add('hide');
      } else {
        header.classList.remove('hide');
      }
    } else {
      header.classList.remove('sticky');
      header.classList.remove('hide');
    }
    
    lastScrollTop = scrollTop;
  });
};

// تأثير معرض الصور
const initGallery = () => {
  const galleryItems = document.querySelectorAll('.gallery-item');
  const modal = document.getElementById('gallery-modal');
  
  if (!galleryItems.length || !modal) return;
  
  const modalImage = modal.querySelector('.modal-image');
  const modalClose = modal.querySelector('.modal-close');
  
  galleryItems.forEach(item => {
    item.addEventListener('click', () => {
      const imgSrc = item.querySelector('img').getAttribute('src');
      modalImage.setAttribute('src', imgSrc);
      modal.classList.add('active');
      document.body.style.overflow = 'hidden';
    });
  });
  
  modalClose.addEventListener('click', () => {
    modal.classList.remove('active');
    document.body.style.overflow = '';
  });
  
  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      modal.classList.remove('active');
      document.body.style.overflow = '';
    }
  });
};

// تهيئة جميع التأثيرات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
  animateOnScroll();
  typeWriter();
  initParticles();
  initWaveBackground();
  initThemeToggle();
  initSmoothScroll();
  initMobileMenu();
  initStickyHeader();
  initGallery();
  
  // تأثير التمرير للعناصر الموجودة
  const sections = document.querySelectorAll('section');
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible');
      }
    });
  }, {
    threshold: 0.1
  });
  
  sections.forEach(section => {
    observer.observe(section);
  });
});
