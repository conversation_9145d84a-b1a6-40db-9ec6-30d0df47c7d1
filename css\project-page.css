/**
 * Project Page Styles
 * Styles for individual project pages
 */

/* Project Hero Section */
.project-hero-section {
    padding: 120px 0 80px;
    position: relative;
    overflow: hidden;
}

.project-hero-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    max-width: 600px;
}

.project-hero-title {
    font-size: var(--text-5xl);
    font-weight: var(--font-bold);
    margin-bottom: var(--spacing-sm);
    line-height: 1.2;
}

.project-hero-category {
    font-size: var(--text-xl);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

.project-hero-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.project-hero-image {
    position: relative;
    margin-top: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.project-hero-image img {
    width: 100%;
    height: auto;
    display: block;
    border-radius: var(--border-radius-lg);
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.project-hero-image:hover img {
    transform: scale(1.02);
    box-shadow: 0 0 20px rgba(157, 78, 221, 0.5);
}

.project-hero-title {
    transition: color var(--transition-normal);
}

.project-hero-title:hover {
    color: var(--accent-primary);
}

/* Project Details Section */
.project-details-section {
    padding: 80px 0;
    background-color: var(--bg-secondary);
}

.project-details-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-xl);
}

.project-description {
    font-size: var(--text-lg);
    line-height: 1.8;
    margin-bottom: var(--spacing-xl);
    color: var(--text-secondary);
}

.features-title {
    font-size: var(--text-2xl);
    font-weight: var(--font-semibold);
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.features-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.features-list li {
    position: relative;
    padding-left: 30px;
    margin-bottom: var(--spacing-md);
    font-size: var(--text-lg);
    color: var(--text-secondary);
}

.features-list li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--accent-primary);
    font-weight: var(--font-bold);
}

.project-info-card {
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    background-color: var(--bg-tertiary);
    box-shadow: var(--shadow-md);
}

.project-info-card h3 {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.project-info-item {
    margin-bottom: var(--spacing-lg);
}

.project-info-item h4 {
    font-size: var(--text-md);
    font-weight: var(--font-medium);
    margin-bottom: var(--spacing-xs);
    color: var(--text-secondary);
}

.project-info-item p {
    font-size: var(--text-lg);
    color: var(--text-primary);
}

.project-technologies {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
}

.project-tech {
    background-color: var(--bg-quaternary);
    color: var(--text-secondary);
    font-size: var(--text-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
}

/* Project Gallery Section */
.project-gallery-section {
    padding: 80px 0;
}

.project-gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.gallery-item {
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    cursor: pointer;
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.gallery-item:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.gallery-item:hover img {
    transform: scale(1.05);
}

/* Related Projects Section */
.related-projects-section {
    padding: 80px 0;
    background-color: var(--bg-secondary);
}

.related-projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.related-project-card {
    background-color: var(--bg-tertiary);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.related-project-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.related-project-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.related-project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.related-project-card:hover .related-project-image img {
    transform: scale(1.1);
}

.related-project-content {
    padding: var(--spacing-lg);
}

.related-project-title {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
    transition: color var(--transition-normal);
}

.related-project-title:hover {
    color: var(--accent-primary);
}

.related-project-category {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

/* Responsive Design */
@media (max-width: 992px) {
    .project-hero-title {
        font-size: var(--text-4xl);
    }

    .project-details-grid {
        grid-template-columns: 1fr;
    }

    .project-hero-actions {
        flex-direction: column;
        align-items: flex-start;
    }

    .project-hero-actions .btn {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .project-hero-section {
        padding: 100px 0 60px;
    }

    .project-hero-title {
        font-size: var(--text-3xl);
    }

    .project-hero-category {
        font-size: var(--text-lg);
    }

    .project-gallery-grid,
    .related-projects-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
}

@media (max-width: 576px) {
    .project-hero-section {
        padding: 80px 0 40px;
    }

    .project-hero-title {
        font-size: var(--text-2xl);
    }

    .project-gallery-grid,
    .related-projects-grid {
        grid-template-columns: 1fr;
    }
}
