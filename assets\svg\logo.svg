<svg width="55" height="65" viewBox="0 0 55 65" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_503_28)">
<path opacity="0.959" fill-rule="evenodd" clip-rule="evenodd" d="M4.54749 0.0301881C6.05349 -0.0274825 7.54399 0.0334732 9.01879 0.21269C12.1365 5.29264 15.2998 10.3419 18.5089 15.3604C18.7126 16.2835 18.8038 17.2264 18.7826 18.1891C18.7522 19.0104 18.7219 19.8317 18.6914 20.6529C17.6088 22.3944 16.4835 24.0977 15.3151 25.763C14.2048 24.0904 13.1402 22.3871 12.1213 20.6529C12.0605 18.5846 11.9996 16.5161 11.9388 14.4479C11.2544 13.2612 10.5244 12.1054 9.7488 10.9803C9.86724 16.1369 9.86724 21.3077 9.7488 26.493C8.03017 26.6728 6.2964 26.7335 4.54749 26.6755C4.54749 17.7937 4.54749 8.91202 4.54749 0.0301881Z" fill="#FEFFFE"/>
<path opacity="0.944" fill-rule="evenodd" clip-rule="evenodd" d="M21.7027 0.0301514C23.2235 0.0301514 24.7445 0.0301514 26.2653 0.0301514C26.2957 8.85121 26.2653 17.6723 26.174 26.493C24.4707 26.7362 22.7672 26.7362 21.0639 26.493C21.0032 21.3221 20.9422 16.1511 20.8814 10.9803C20.5278 11.5077 20.1323 11.9943 19.6952 12.4403C18.6991 10.9655 17.7258 9.475 16.7751 7.96899C18.3511 5.27161 19.9936 2.62533 21.7027 0.0301514Z" fill="#FEFFFE"/>
</g>
<g filter="url(#filter1_d_503_28)">
<path opacity="0.956" fill-rule="evenodd" clip-rule="evenodd" d="M29.5503 0.0301699C31.3145 0.0301699 33.0786 0.0301699 34.8429 0.0301699C34.8388 3.48731 34.8388 6.89407 34.8429 10.2503C37.8523 6.96671 40.8331 3.65138 43.7855 0.303923C46.018 0.03747 48.2688 -0.0537811 50.538 0.0301699C50.5672 0.340241 50.5369 0.644472 50.4468 0.94268C45.2139 6.47979 40.0431 12.0766 34.9341 17.7329C34.8733 20.6529 34.8124 23.5729 34.7516 26.493C33.033 26.6727 31.2992 26.7335 29.5503 26.6755C29.5503 17.7936 29.5503 8.912 29.5503 0.0301699Z" fill="#FEFFFE"/>
<path opacity="0.951" fill-rule="evenodd" clip-rule="evenodd" d="M41.2304 14.4478C41.896 14.4701 42.4132 14.7741 42.7817 15.3603C45.3367 18.6454 47.8918 21.9304 50.4468 25.2154C50.5375 25.6983 50.568 26.1851 50.5381 26.6755C48.6331 26.7578 46.7473 26.6665 44.8805 26.4017C42.66 23.8162 40.4397 21.2309 38.2192 18.6454C38.0974 18.4021 38.0974 18.1586 38.2192 17.9154C39.2611 16.7829 40.2648 15.627 41.2304 14.4478Z" fill="#FEFFFE"/>
</g>
<g filter="url(#filter2_d_503_28)">
<path opacity="0.956" fill-rule="evenodd" clip-rule="evenodd" d="M11.3001 29.778C16.2716 29.7182 21.2295 29.7789 26.174 29.9605C26.3404 36.23 26.2797 42.4959 25.9915 48.7582C24.8452 53.1288 22.0772 55.8358 17.6877 56.8796C13.308 56.9708 8.92792 57.0013 4.54751 56.9708C4.48929 55.1611 4.55006 53.3664 4.73001 51.587C8.74925 51.6407 12.673 51.671 16.5014 51.6783C18.7511 51.5261 20.2111 50.3703 20.8814 48.2107C20.9727 43.892 21.0032 39.5727 20.9727 35.2531C17.8696 35.2226 14.7671 35.2531 11.6651 35.3443C9.12137 36.5404 8.60435 38.335 10.1138 40.7281C10.4934 41.0243 10.8887 41.2981 11.3001 41.5494C13.7808 41.8133 16.2751 41.9045 18.7827 41.8232C18.8407 43.5721 18.7799 45.3058 18.6002 47.0245C16.0451 47.1462 13.4901 47.1462 10.9351 47.0245C6.58879 45.8709 4.27704 43.0421 4 38.5381C4.16151 34.8348 5.86481 32.1277 9.11006 30.4168C9.84792 30.1447 10.5779 29.9317 11.3001 29.778Z" fill="#FEFFFE"/>
</g>
<g filter="url(#filter3_d_503_28)">
<path opacity="0.967" fill-rule="evenodd" clip-rule="evenodd" d="M36.3029 29.778C41.0479 29.778 45.793 29.778 50.538 29.778C50.5685 35.9835 50.538 42.1885 50.4468 48.3932C49.329 52.5545 46.7435 55.3225 42.6904 56.6971C38.3184 56.9617 33.9384 57.053 29.5503 56.9708C29.4921 55.1611 29.5528 53.3664 29.7328 51.587C33.3792 51.6898 37.0292 51.6898 40.6829 51.587C43.4867 51.399 45.038 49.9085 45.3367 47.1157C45.428 43.162 45.4585 39.2077 45.428 35.2531C42.5074 35.2226 39.5874 35.2531 36.6679 35.3443C34.2322 36.324 33.5934 37.997 34.7516 40.3631C35.286 41.0714 35.9855 41.5277 36.8504 41.7319C38.857 41.8232 40.8645 41.8536 42.8729 41.8232C42.8729 43.5874 42.8729 45.3515 42.8729 47.1157C40.5605 47.1462 38.2487 47.1157 35.9379 47.0245C32.1637 46.0484 29.8824 43.6456 29.094 39.8156C28.7851 35.9628 30.2148 32.9515 33.3828 30.7818C34.3346 30.3126 35.3079 29.978 36.3029 29.778Z" fill="#FEFFFE"/>
</g>
<defs>
<filter id="filter0_d_503_28" x="0.547485" y="0.00915527" width="29.7292" height="34.6876" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_503_28"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_503_28" result="shape"/>
</filter>
<filter id="filter1_d_503_28" x="25.5503" y="0" width="28.9989" height="34.7047" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_503_28"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_503_28" result="shape"/>
</filter>
<filter id="filter2_d_503_28" x="0" y="29.7557" width="30.2654" height="35.2265" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_503_28"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_503_28" result="shape"/>
</filter>
<filter id="filter3_d_503_28" x="25.0529" y="29.778" width="29.4966" height="35.222" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_503_28"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_503_28" result="shape"/>
</filter>
</defs>
</svg>
