/*
 * تأثيرات 2025 المتطورة
 * Advanced 2025 Effects and Animations
 */

/* ===== Neomorphism 2.0 ===== */
.neo-card-2025 {
  background: linear-gradient(145deg, #1e1e2e, #2a2a3e);
  border-radius: 20px;
  box-shadow: 
    var(--neo-shadow-light),
    var(--neo-shadow-inset),
    var(--neo-glow);
  
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.neo-card-2025::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--neural-pattern);
  opacity: 0.1;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.neo-card-2025:hover::before {
  opacity: 0.2;
}

.neo-card-2025:hover {
  transform: translateY(-5px);
  box-shadow: 
    var(--neo-shadow-light),
    var(--neo-shadow-inset),
    0 0 40px rgba(157, 78, 221, 0.4);
}

/* ===== Holographic Effects ===== */
.holographic {
  background: var(--holographic-gradient);
  background-size: 200% 200%;
  animation: holographicShift 3s ease-in-out infinite;
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  position: relative;
}

@keyframes holographicShift {
  0%, 100% { 
    background-position: 0% 50%; 
    filter: hue-rotate(0deg);
  }
  25% { 
    background-position: 100% 0%; 
    filter: hue-rotate(90deg);
  }
  50% { 
    background-position: 100% 100%; 
    filter: hue-rotate(180deg);
  }
  75% { 
    background-position: 0% 100%; 
    filter: hue-rotate(270deg);
  }
}

.holographic-border {
  position: relative;
  border-radius: 12px;
  padding: 2px;
  background: var(--holographic-gradient);
  background-size: 200% 200%;
  animation: holographicShift 3s ease-in-out infinite;
}

.holographic-border::before {
  content: '';
  position: absolute;
  inset: 2px;
  background: var(--bg-primary);
  border-radius: 10px;
  z-index: -1;
}

/* ===== AI Neural Network Animation ===== */
.neural-bg {
  position: relative;
  overflow: hidden;
}

.neural-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--neural-pattern);
  animation: neuralPulse 4s ease-in-out infinite;
  z-index: -1;
}

@keyframes neuralPulse {
  0%, 100% { 
    opacity: 0.3; 
    transform: scale(1) rotate(0deg); 
  }
  50% { 
    opacity: 0.7; 
    transform: scale(1.1) rotate(180deg); 
  }
}

.neural-nodes {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.neural-node {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--cyber-green);
  border-radius: 50%;
  box-shadow: 0 0 10px var(--cyber-green);
  animation: nodeFloat 6s ease-in-out infinite;
}

@keyframes nodeFloat {
  0%, 100% { transform: translateY(0px) scale(1); opacity: 0.7; }
  50% { transform: translateY(-20px) scale(1.2); opacity: 1; }
}

/* ===== Brutalism الناعم ===== */
.brutal-soft {
  background: var(--holographic-pink);
  border: 4px solid #000;
  box-shadow: 
    8px 8px 0px #ff5252,
    16px 16px 0px rgba(0, 0, 0, 0.1);
  transform: rotate(-2deg);
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  position: relative;
  overflow: hidden;
}

.brutal-soft::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  transition: transform 0.6s ease;
}

.brutal-soft:hover {
  transform: rotate(0deg) scale(1.05);
  box-shadow: 
    12px 12px 0px #ff5252,
    24px 24px 0px rgba(0, 0, 0, 0.1);
}

.brutal-soft:hover::before {
  transform: rotate(45deg) translateX(100%);
}

/* ===== Glassmorphism 2.0 ===== */
.glass-2025 {
  background: rgba(18, 18, 30, 0.7);
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.glass-2025::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.6s ease;
}

.glass-2025:hover::before {
  left: 100%;
}

/* ===== Magnetic Effect ===== */
.magnetic {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.magnetic:hover {
  transform: scale(1.05) translateZ(0);
}

/* ===== 3D Tilt Effect ===== */
.tilt-3d {
  transform-style: preserve-3d;
  transition: transform 0.3s ease;
}

.tilt-3d:hover {
  transform: perspective(1000px) rotateX(5deg) rotateY(5deg);
}

/* ===== Parallax Cards ===== */
.parallax-card {
  transform-style: preserve-3d;
  transition: transform 0.3s ease;
  position: relative;
}

.parallax-card:hover {
  transform: rotateX(5deg) rotateY(5deg);
}

.parallax-card .card-content {
  transform: translateZ(50px);
  transition: transform 0.3s ease;
}

.parallax-card:hover .card-content {
  transform: translateZ(80px);
}

/* ===== Floating Animation ===== */
.float-animation {
  animation: floatUpDown 6s ease-in-out infinite;
}

@keyframes floatUpDown {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.float-animation:nth-child(2) {
  animation-delay: 2s;
}

.float-animation:nth-child(3) {
  animation-delay: 4s;
}
