/*
 * Advanced Theme System 2025
 * Smart Dark/Light mode with smooth transitions and auto-detection
 */

/* ===== CSS Custom Properties for Themes ===== */
:root {
  /* Theme transition duration */
  --theme-transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Color scheme detection */
  color-scheme: dark light;
}

/* ===== Dark Theme (Default) ===== */
[data-theme="dark"] {
  /* Backgrounds */
  --bg-primary: #0a0a12;
  --bg-secondary: #12121e;
  --bg-tertiary: #1a1a2e;
  --bg-glass: rgba(18, 18, 30, 0.8);
  --bg-card: rgba(26, 26, 46, 0.9);
  
  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #e2e8f0;
  --text-tertiary: #a0aec0;
  --text-muted: #718096;
  
  /* Accent Colors */
  --accent-primary: #9d4edd;
  --accent-secondary: #00b4d8;
  --accent-tertiary: #7209b7;
  --gold-accent: #ffd700;
  
  /* AI Colors */
  --neural-blue: #0066ff;
  --quantum-purple: #6b46c1;
  --cyber-green: #00ff88;
  --holographic-pink: #ff006e;
  --matrix-gold: #ffd700;
  
  /* Shadows */
  --shadow-color: rgba(0, 0, 0, 0.5);
  --glow-color: rgba(157, 78, 221, 0.3);
  
  /* Borders */
  --border-color: rgba(255, 255, 255, 0.1);
  --border-hover: rgba(157, 78, 221, 0.5);
}

/* ===== Light Theme ===== */
[data-theme="light"] {
  /* Backgrounds */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-glass: rgba(248, 250, 252, 0.8);
  --bg-card: rgba(255, 255, 255, 0.9);
  
  /* Text Colors */
  --text-primary: #1a202c;
  --text-secondary: #2d3748;
  --text-tertiary: #4a5568;
  --text-muted: #718096;
  
  /* Accent Colors */
  --accent-primary: #7c3aed;
  --accent-secondary: #0891b2;
  --accent-tertiary: #5b21b6;
  --gold-accent: #f59e0b;
  
  /* AI Colors */
  --neural-blue: #3b82f6;
  --quantum-purple: #8b5cf6;
  --cyber-green: #10b981;
  --holographic-pink: #ec4899;
  --matrix-gold: #f59e0b;
  
  /* Shadows */
  --shadow-color: rgba(0, 0, 0, 0.1);
  --glow-color: rgba(124, 58, 237, 0.2);
  
  /* Borders */
  --border-color: rgba(0, 0, 0, 0.1);
  --border-hover: rgba(124, 58, 237, 0.3);
}

/* ===== Auto Theme Detection ===== */
@media (prefers-color-scheme: dark) {
  :root:not([data-theme]) {
    /* Apply dark theme variables */
    --bg-primary: #0a0a12;
    --bg-secondary: #12121e;
    --bg-tertiary: #1a1a2e;
    --text-primary: #ffffff;
    --text-secondary: #e2e8f0;
    --accent-primary: #9d4edd;
  }
}

@media (prefers-color-scheme: light) {
  :root:not([data-theme]) {
    /* Apply light theme variables */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --text-primary: #1a202c;
    --text-secondary: #2d3748;
    --accent-primary: #7c3aed;
  }
}

/* ===== Theme Transition Effects ===== */
*,
*::before,
*::after {
  transition: 
    background-color var(--theme-transition),
    color var(--theme-transition),
    border-color var(--theme-transition),
    box-shadow var(--theme-transition),
    opacity var(--theme-transition);
}

/* Exclude specific elements from transitions */
.no-theme-transition,
.no-theme-transition *,
canvas,
video,
iframe {
  transition: none !important;
}

/* ===== Theme Toggle Button ===== */
.theme-toggle-advanced {
  position: relative;
  width: 60px;
  height: 30px;
  background: var(--bg-tertiary);
  border: 2px solid var(--border-color);
  border-radius: 30px;
  cursor: pointer;
  transition: all var(--theme-transition);
  overflow: hidden;
}

.theme-toggle-advanced::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 22px;
  height: 22px;
  background: var(--accent-primary);
  border-radius: 50%;
  transition: all var(--theme-transition);
  box-shadow: 0 2px 8px var(--glow-color);
}

.theme-toggle-advanced.light::before {
  transform: translateX(28px);
  background: var(--matrix-gold);
}

/* Theme toggle icons */
.theme-toggle-advanced .icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  transition: all var(--theme-transition);
  pointer-events: none;
}

.theme-toggle-advanced .icon-sun {
  right: 6px;
  color: var(--matrix-gold);
  opacity: 0;
}

.theme-toggle-advanced .icon-moon {
  left: 6px;
  color: var(--accent-primary);
  opacity: 1;
}

.theme-toggle-advanced.light .icon-sun {
  opacity: 1;
}

.theme-toggle-advanced.light .icon-moon {
  opacity: 0;
}

/* ===== Theme-aware Components ===== */

/* Cards */
.theme-card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  box-shadow: 0 4px 12px var(--shadow-color);
}

.theme-card:hover {
  border-color: var(--border-hover);
  box-shadow: 0 8px 25px var(--glow-color);
}

/* Buttons */
.btn-theme {
  background: var(--accent-primary);
  color: var(--bg-primary);
  border: none;
  transition: all var(--theme-transition);
}

.btn-theme:hover {
  background: var(--accent-secondary);
  box-shadow: 0 4px 15px var(--glow-color);
}

/* Inputs */
.input-theme {
  background: var(--bg-secondary);
  border: 2px solid var(--border-color);
  color: var(--text-primary);
  transition: all var(--theme-transition);
}

.input-theme:focus {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px var(--glow-color);
  outline: none;
}

.input-theme::placeholder {
  color: var(--text-muted);
}

/* Navigation */
.nav-theme {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
}

.nav-link-theme {
  color: var(--text-secondary);
  transition: all var(--theme-transition);
}

.nav-link-theme:hover,
.nav-link-theme.active {
  color: var(--accent-primary);
}

/* ===== Theme Animation Effects ===== */
.theme-switching {
  position: relative;
  overflow: hidden;
}

.theme-switching::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    var(--accent-primary),
    transparent
  );
  opacity: 0.1;
  animation: themeSwitchWave 0.6s ease-out;
  pointer-events: none;
  z-index: 9999;
}

@keyframes themeSwitchWave {
  0% {
    left: -100%;
    opacity: 0;
  }
  50% {
    opacity: 0.1;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}

/* ===== Accessibility Improvements ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    transition-duration: 0.01ms !important;
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --border-color: currentColor;
    --shadow-color: transparent;
  }
  
  .theme-card {
    border-width: 2px;
  }
}

/* ===== Theme-specific Overrides ===== */

/* Dark theme specific styles */
[data-theme="dark"] .holographic-advanced {
  filter: brightness(1.2) contrast(1.1);
}

[data-theme="dark"] .neon-text-2025 {
  text-shadow: 
    0 0 5px currentColor,
    0 0 10px currentColor,
    0 0 15px currentColor;
}

/* Light theme specific styles */
[data-theme="light"] .holographic-advanced {
  filter: brightness(0.9) contrast(1.2);
}

[data-theme="light"] .neon-text-2025 {
  text-shadow: 
    0 2px 4px var(--shadow-color),
    0 0 8px var(--glow-color);
}

[data-theme="light"] .brutal-card {
  box-shadow: 
    4px 4px 0px var(--accent-primary),
    8px 8px 0px var(--shadow-color);
}

[data-theme="light"] .glass-2025 {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

/* ===== Theme Loading State ===== */
.theme-loading {
  opacity: 0.7;
  pointer-events: none;
  transition: opacity 0.2s ease;
}

.theme-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent 0%,
    var(--accent-primary) 50%,
    transparent 100%
  );
  opacity: 0.1;
  animation: themeLoadingShimmer 1.5s ease-in-out infinite;
}

@keyframes themeLoadingShimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}
